<?php

/**
 * 检查外挂状态的队列任务
 * @desc 检查外挂状态的队列任务
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/15
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs;

use App\Models\MySQL\SwitchConfig;
use App\Models\MySQL\Whitelist;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Services\StarRocksService;
use App\Utils\RedisKey;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class CheckPluginStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 在超时之前任务可以运行的秒数
     *
     * @var int
     */
    public $timeout = 1800;

    /**
     * 获取条数
     *
     * @var int
     */
    const LIMIT = 1000;

    /**
     * 配置信息
     *
     * @var array
     */
    const CONFIG = [
        'port_info' => ['/12020/', '/12030/'],
        'socket_info' => ['/@[^@]*\.event\.localserver/', '/@[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i'],
        'click_info' => [['area' => 0, 'pressure' => 0], ['area' => 1, 'pressure' => 1]],
    ];

    /**
     * 开始时间
     *
     * @var string
     */
    private $startDate;

    /**
     * 设备id白名单
     * @var Whitelist
     */
    private $devWhitelist;
    /**
     * ip白名单
     * @var Whitelist
     */
    private $ipWhitelist;
    /**
     * 账号id白名单
     * @var Whitelist
     */
    private $accountIdWhitelist;

    /**
     * 插入的数据
     *
     * @var array
     */
    private $insertData = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($startDate)
    {
        $this->startDate = $startDate;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //设置内存大小
        ini_set('memory_limit', '2048M');
        //当前offset值
        $offset = 0;
        // 上次执行的key
        $lastExecuteKey = RedisKey::getPluginLogDateKey();
        // 从缓存中读取上一次执行的时间
        $lastExecuteTime = Redis::get($lastExecuteKey);
        // 判断是否为空，为空则取startDate
        if (empty($lastExecuteTime)) {
            $lastExecuteTime = $this->startDate;
        }
        //打印日志
        Log::info("执行检查外挂状态的Job，startDate值：{$this->startDate}，上次执行时间：{$lastExecuteTime}， 开始时间：" . Carbon::now()->toDateTimeString());
        //循环获取
        while (true) {
            //获取该时间下，所有外挂数据
            $initTable = AntiChectInit::TABLE_NAME;
            $dataTable = AntiChectUploadDataPrimary::TABLE_NAME;
            $list = AntiChectUploadDataPrimary::query()
                ->selectRaw("{$dataTable}.session_id, port_info, click_info, get_json_string(game_info, 'account_id') as account_id, {$dataTable}.server_dev_str, {$initTable}.ip, {$initTable}.extra_app_id, {$initTable}.os_type as os_type, {$dataTable}.stream_date")
                ->where("{$dataTable}.stream_date", '>=', $lastExecuteTime)
                ->leftJoin($initTable, "{$dataTable}.session_id", '=', "{$initTable}.session_id")
                ->orderBy("{$dataTable}.stream_date")
                ->limit(self::LIMIT)
                ->offset($offset)
                ->getFromSR();
            //判断数据是否为空
            if (empty($list)) {
                break;
            }
            //增加当前offset值
            $offset += self::LIMIT;
            // 设置时间
            $lastExecuteTime = end($list)['stream_date'];
            // // 查询白名单数据
            // $developerAppIds = array_unique(array_column($list, 'extra_app_id'));
            // $serverDevStr = array_unique(array_column($list, 'server_dev_str'));
            // $ip = array_unique(array_column($list, 'ip'));
            // $accountId = array_unique(array_column($list, 'account_id'));
            // $this->devWhitelist = $this->getWhitelist($developerAppIds, Whitelist::WHITELIST_TYPE_SERVICE_DEV_STR, $serverDevStr); // 设备id白名单
            // $this->ipWhitelist = $this->getWhitelist($developerAppIds, Whitelist::WHITELIST_TYPE_IP, $ip); // ip白名单
            // $this->accountIdWhitelist = $this->getWhitelist($developerAppIds, Whitelist::WHITELIST_TYPE_ACCOUNT_ID, $accountId); // 账号id

            //打印日志
            Log::info("检查外挂状态的Job数据成功，startDate值：{$this->startDate}，记录条数：" . count($list));
            foreach ($list as $data) {
                //进行检测
                $this->checkData($data);
            }
            //判断是否需要写入数据
            if (!empty($this->insertData)) {
                //写入数据
                $this->insertData();
            }
        }
        // 从缓存中读取执行的时间
        $executeTime = Redis::get($lastExecuteKey);
        // 判断为空，并且时间大于缓存的时间
        if (empty($executeTime) || $executeTime < $lastExecuteTime) {
            // 更新缓存的时间
            Redis::set($lastExecuteKey, $lastExecuteTime);
        }
        //打印日志
        Log::info("执行检查外挂状态的Job，startDate值：{$this->startDate}，结束时间：" . Carbon::now()->toDateTimeString());
    }

    /**
     * 检查数据
     *
     * @param $data
     * @return void
     */
    private function checkData($data)
    {
        //保存结果数据
        $result = [
            'port_info' => [],
            'socket_info' => [],
            'click_info' => [
                'ratio' => 0,
                'click_num' => 0,
                'match_num' => 0,
            ],
        ];
        $isPlugin = false;
        //1、检查开放端口是否包含12020或12030
        foreach (self::CONFIG['port_info'] as $pattern) {
            if (preg_match($pattern, $data['port_info'], $matches)) {
                $isPlugin = true;
                $result['port_info'][] = $matches[0];
            }
        }
        //2、检查套接字名是否包含@xxx.event.localserver或@uuid
        foreach (self::CONFIG['socket_info'] as $pattern) {
            if (preg_match($pattern, $data['port_info'], $matches)) {
                $isPlugin = true;
                $result['socket_info'][] = $matches[0];
            }
        }
        //3、检查点击压力和点击面积，满足80%
        $clickInfo = json_decode($data['click_info'], true);
        foreach ($clickInfo as $item) {
            //判断是否点击事件，不是则跳过
            if ($item['type'] != 'click') {
                continue;
            }
            //点击次数+1
            $result['click_info']['click_num']++;
            //对规则进行校验
            foreach (self::CONFIG['click_info'] as $clickItem) {
                //判断是否匹配
                if ($item['area'] == $clickItem['area'] && $item['pressure'] == $clickItem['pressure']) {
                    //匹配次数+1
                    $result['click_info']['match_num']++;
                }
            }
        }
        //判断是否满足80%
        if ($result['click_info']['click_num'] > 0) {
            $result['click_info']['ratio'] = bcmul(bcdiv($result['click_info']['match_num'], $result['click_info']['click_num'], 6), 100, 2);
            if ($result['click_info']['ratio'] >= 80) {
                $isPlugin = true;
            }
        }

        // 4、检查是否在白名单内
        $action = 0;
        $title = "";
        // if ($isPlugin) {
        //     $isContainWhitelist = $this->isContainWhitelist($data);
        //     if ($isContainWhitelist) { // 在白名单内
        //         // todo 需要将action改为 "白名单放过"
        //         $action = SwitchConfig::EXEC_ACTION_IP_WHITELIST;
        //     }
        // }

        //5、如果是外挂，
        if ($isPlugin) {
            // 如果action不是白名单放过，那么就读取配置信息获取action值
            if ($action != SwitchConfig::EXEC_ACTION_IP_WHITELIST) {
                try {
                    // 获取开关配置
                    $switchInfo = [];
                    $switchRedisList = Redis::connection('api')->get(RedisKey::getPluginSwitchInfoKey($data['extra_app_id']));
                    if (!empty($switchRedisList)) {
                        $switchList = json_decode($switchRedisList, true);
                        if (!empty($switchList)) {
                            $switchList = array_column($switchList, null, "os_type");
                            if (isset($switchList[$data['os_type']])) {
                                $switchInfo = $switchList[$data['os_type']];
                            }
                        }
                    }
                    // 获取不到配置，使用默认配置
                    if (empty($switchInfo)) {
                        $switchInfo = $this->getDefaultChectConfig($data['os_type']);
                    }

                    $action = $switchInfo["exec_action"];
                    if ($action == SwitchConfig::EXEC_ACTION_POPUP) {
                        // 只有action为弹窗提示，title才有值
                        $title = $switchInfo["popup_content"];
                    }
                } catch (\Exception $e) {
                    Log::error('处理开关配置信息错误,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
                }
            }
            //写入到redis
            Redis::connection('api')->setEx(RedisKey::getPluginSessionResultKey($data['session_id']), 3600, json_encode([
                'action' => $action,
                'title' => $title,
            ]));
            $this->insertData[$data['session_id']] = [
                'stream_date' => date('Y-m-d H:i:s'),
                'session_id' => $data['session_id'],
                'port_info' => json_encode($result['port_info'], JSON_UNESCAPED_UNICODE),
                'socket_info' => json_encode($result['socket_info'], JSON_UNESCAPED_UNICODE),
                'click_info' => json_encode($result['click_info'], JSON_UNESCAPED_UNICODE),
                'action' => $action,
                'title' => $title,
                'extra_app_id' => $data['extra_app_id'],
                'server_dev_str' => $data['server_dev_str'],
                'ip' => $data['ip'],
                'account_id' => $data['account_id']
            ];
        }
    }

    /**
     * 写入数据
     *
     * @param $result
     * @param $fileName
     * @return void
     */
    private function insertData()
    {
        //打印日志
        Log::info("执行检查外挂状态的Job，startDate值：{$this->startDate}，写入数据：" . count($this->insertData));
        $columns = array_keys(current($this->insertData));
        $values = [];
        foreach ($this->insertData as $row) {
            $rowTemp = [];
            foreach ($row as $key => $val) {
                if (in_array($key, ['port_info', 'socket_info'])) {
                    $rowTemp[] = $val;
                } else {
                    $rowTemp[] = "'{$val}'";
                }
            }
            $values[] = "(" . implode(",", array_values($rowTemp)) . ")";
        }
        $sql = "INSERT INTO anti_chect_hit_log (" . implode(", ", $columns) . ") VALUES " . implode(", ", $values);
        (new StarRocksService())->execute($sql);
        //重置数组
        $this->insertData = [];
    }

    /**
     * 检查是否在白名单内
     * @param $data
     * @return bool
     */
    private function isContainWhitelist($data)
    {
        $isContainWhitelist = false; // 是否在白名单内
        // 设备id白名单判断
        if (!empty($data['server_dev_str'])) {
            foreach ($this->devWhitelist as $whitelist) { // 遍历设备白名单列表
                if ($whitelist->developer_app_id == $data['extra_app_id'] && $whitelist->server_dev_str == $data['server_dev_str']) {
                    $isContainWhitelist = true;
                    break;
                }
            }
        }
        // ip白名单判断
        if (!empty($data['ip'])) {
            foreach ($this->ipWhitelist as $whitelist) {
                if ($whitelist->developer_app_id == $data['extra_app_id'] && $whitelist->ip == $data['ip']) {
                    $isContainWhitelist = true;
                    break;
                }
            }
        }
        // 账号id白名单判断
        if (!empty($data['account_id'])) {
            foreach ($this->accountIdWhitelist as $whitelist) {
                if ($whitelist->developer_app_id == $data['extra_app_id'] && $whitelist->account_id == $data['account_id']) {
                    $isContainWhitelist = true;
                    break;
                }
            }
        }
        return $isContainWhitelist;
    }

    /**
     * 获取白名单数据
     * @param $developerAppIds
     * @param $whitelistType
     * @param $whereData
     * @return mixed
     */
    private function getWhitelist($developerAppIds, $whitelistType, $whereData)
    {
        return Whitelist::query()
            ->whereIn(Whitelist::WHITELIST_TYPE_FIELD_MAP[$whitelistType], $whereData)
            ->whereIn('developer_app_id', $developerAppIds)
            ->where('status', Whitelist::WHITELIST_STATUS_VALID)
            ->get();
    }

    /**
     * 使用反外挂默认配置
     * @param $osType
     * @return void
     */
    protected function getDefaultChectConfig($osType)
    {
        if ($osType == 1) {
            $switchInfo = SwitchConfig::FILL_ANDROID_DEFAULT_CONFIG;
        } else {
            $switchInfo = SwitchConfig::FILL_IOS_DEFAULT_CONFIG;
        }

        return $switchInfo;
    }
}
