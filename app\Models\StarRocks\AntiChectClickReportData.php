<?php

/**
 * 对应starRocks数仓中anti_chect_click_report_data表
 * @desc 对应starRocks数仓中anti_chect_click_report_data表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

class AntiChectClickReportData extends BaseModel
{
    public const TABLE_NAME = 'anti_chect_click_report_data';

    protected $table = self::TABLE_NAME;
}
