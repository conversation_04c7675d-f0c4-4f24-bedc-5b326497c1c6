<?php

/**
 * 外挂检测控制器类
 * @desc 外挂检测控制器类
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Http\Validation\PluginValidation;
use App\Jobs\ListExportJob;
use App\Models\StarRocks\AntiChectInit;
use App\Services\Plugin\DetailService;
use App\Services\Plugin\ListService;
use App\Services\Plugin\SearchService;
use App\Utils\RedisKey;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class PluginController extends Controller
{
    /**
     * 列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12568
     * @return array
     * @throws ValidationException
     */
    public function list(): array
    {
        //校验参数
        $params = PluginValidation::build()
            ->startDate()->endDate()->osType()->accountId()->serverDevStr()
            ->ip()->serverId()->roleId()->appPackageName()->sdkVer()->appVersion()
            ->action()->riskLevel()->developerAppId()->sortField()->sortType()
            ->roleName()->page()->prePage()->reportType()->validate();
        try {
            //查询数据
            $result = (new ListService($params))->getData();
            //返回数据
            return $this->success($result);
        } catch (Exception $e) {
            Log::error('外挂检测报告列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 搜索下拉列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12581
     * @return array
     * @throws ValidationException
     */
    public function search(): array
    {
        //校验参数
        $params = PluginValidation::build()
            ->startDate()->endDate()->osType()->accountId()->serverDevStr()
            ->ip()->serverId()->roleId()->appPackageName()->sdkVer()->appVersion()
            ->action()->riskLevel()->developerAppId()->roleName()
            ->validate();
        try {
            //查询数据
            $data = (new SearchService($params))->getData();
            //返回数据
            return $this->success($data);
        } catch (Exception $e) {
            Log::error('外挂检测报告搜索接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 详情
     *
     * @return array
     * @throws ValidationException
     */
    public function detail(): array
    {
        //校验参数
        $params = PluginValidation::build()->sessionId()->validate();
        try {
            //查询数据
            $result = AntiChectInit::query()->where('session_id', $params['session_id'])->firstFromSR();
            //判断是否为空
            if (empty($result)) {
                return $this->fail(Response::HTTP_UNPROCESSABLE_ENTITY, trans('response.外挂检测报告找不到'));
            }
            //查询数据
            $data = (new DetailService($result))->getData();
            //返回数据
            return $this->success($data);
        } catch (Exception $e) {
            Log::error('外挂检测报告详情接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导出数据
     *
     * @return array
     * @throws ValidationException
     */
    public function exportData(): array
    {
        //校验参数
        $params = PluginValidation::build()
            ->startDate()->endDate()->osType()->accountId()->serverDevStr()
            ->ip()->serverId()->roleId()->appPackageName()->sdkVer()->appVersion()
            ->action()->riskLevel()->developerAppId()->sortField()->sortType()
            ->roleName()->page()->prePage()->validate();

        try {
            //查询数据
            $taskId = Str::uuid();
            // 创建导出队列
            dispatch(new ListExportJob($taskId, $params))->onQueue('check_plugin_status');
            //返回数据
            return $this->success([
                'task_id' => $taskId,
            ]);
        } catch (Exception $e) {
            Log::error('外挂检测报告导出数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导出状态
     *
     * @return array
     * @throws ValidationException
     */
    public function exportStatus(): array
    {
        // 校验参数
        $params = PluginValidation::build()->taskId()->validate();

        try {
            // 查询数据
            $result = Redis::get(RedisKey::getPluginExportDataKey($params['task_id']));
            // 进度值
            $progress = 0;
            // 判断数据是否为空
            if (!empty($result)) {
                $result = json_decode($result, true);
                // 判断code是否为1，1代表发生错误
                if ($result['code'] == 1) {
                    return $this->fail(Response::HTTP_BAD_REQUEST, $result['msg']);
                }
                // 获取进度
                $progress = $result['progress'];
            }
            // 返回数据
            return $this->success([
                'progress' => $progress,
                'download_url' => config('app.url') . "/plugin/export/download?task_id={$params['task_id']}",
            ]);
        } catch (Exception $e) {
            Log::error('外挂检测报告导出状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 下载接口
     *
     * @return array|Response
     * @throws ValidationException
     */
    public function exportDownload()
    {
        //校验参数
        $params = PluginValidation::build()->taskId()->validate();

        try {
            // 查询数据
            $result = Redis::get(RedisKey::getPluginExportDataKey($params['task_id']));
            // 判断数据是否为空
            if (!empty($result)) {
                $result = json_decode($result, true);
                // 判断code是否为1，1代表发生错误
                if ($result['code'] == 1) {
                    return $this->fail(Response::HTTP_BAD_REQUEST, $result['msg']);
                }
                // 判断文件路径是否为空
                if (!empty($result['path'])) {
                    // 获取文件路径，返回浏览器下载
                    return Storage::download(str_replace('app/', '', $result['path']));
                }
            }
            // 返回数据
            return $this->fail(Response::HTTP_BAD_REQUEST, trans('response.导出文件失败'));
        } catch (Exception $e) {
            Log::error('外挂检测报告下载接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
