# 柱状图数据API接口文档

## 接口概述

柱状图数据API提供外挂检测系统的多维度统计分析功能，支持风险等级分布、游戏上传类型分析、Root状态统计和重签包统计等功能。

**基础信息：**
- 接口域名：`{domain}`
- 接口前缀：`/overview`
- 认证方式：需要通过auth中间件认证
- 数据格式：JSON

---

## 获取柱状图数据

**简要描述：** 获取外挂检测数据的完整柱状图统计信息

**请求URL：** `/overview/barCharData`

**请求方式：** GET

**参数：**

| 参数名 | 必选 | 类型 | 说明 |
|:----    |:---|:----- |-----   |
| extra_app_id | 是 | int | 应用ID |
| start_date | 否 | string | 开始时间，格式：Y-m-d H:i:s |
| end_date | 否 | string | 结束时间，格式：Y-m-d H:i:s |
| os_type | 否 | int | 平台类型 |

**返回示例：**

```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "risk_and_upload_data": {
            "risk_level": {
                "low_risk": 100,
                "medium_risk": 50,
                "high_risk": 20
            },
            "upload_type": {
                "game_upload": 120,
                "resign_count": 30,
                "ai_count": 20
            },
            "root_status": {
                "is_root": 80,
                "not_root": 90
            }
        },
        "sign_package_count": [
            {
                "name": "com.example.app1",
                "num": 15
            },
            {
                "name": "com.example.app2", 
                "num": 10
            }
        ]
    }
}
```

**返回参数说明：**

| 参数名 | 类型 | 说明 |
|:-----  |:-----|-----                           |
| risk_level | object | 风险等级分布 |
| risk_level.low_risk | int | 低风险数量 |
| risk_level.medium_risk | int | 中风险数量 |
| risk_level.high_risk | int | 高风险数量 |
| upload_type | object | 上传类型分布 |
| upload_type.game_upload | int | 正常游戏上传数量 |
| upload_type.resign_count | int | 重签包上传数量 |
| upload_type.ai_count | int | AI检测上传数量 |
| root_status | object | Root状态分布 |
| root_status.is_root | int | 已Root设备数量 |
| root_status.not_root | int | 未Root设备数量 |
| sign_package_count | array | 重签包统计列表 |
| sign_package_count[].name | string | 包名 |
| sign_package_count[].num | int | 出现次数 |

---

## 错误码说明

| 错误码 | 说明 |
|:-----  |-----                           |
| 200 | 请求成功 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

**错误返回示例：**

```json
{
    "code": 422,
    "msg": "参数验证失败",
    "data": {
        "extra_app_id": ["应用ID不能为空"]
    }
}
```

---

## 请求示例

### cURL示例

```bash
curl -X GET \
  "http://your-domain.com/bar-chart/data?extra_app_id=12345&start_date=2024-01-01%2000:00:00&end_date=2024-01-31%2023:59:59&risk_level[]=2&risk_level[]=3" \
  -H 'Authorization: Bearer your-token'
```

### JavaScript示例

```javascript
const params = new URLSearchParams({
    extra_app_id: 12345,
    start_date: '2024-01-01 00:00:00',
    end_date: '2024-01-31 23:59:59'
});
params.append('risk_level[]', 2);
params.append('risk_level[]', 3);

const response = await fetch(`/bar-chart/data?${params}`, {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer your-token'
    }
});

const data = await response.json();
console.log(data);
```

### PHP示例

```php
$params = [
    'extra_app_id' => 12345,
    'start_date' => '2024-01-01 00:00:00',
    'end_date' => '2024-01-31 23:59:59',
    'risk_level' => [2, 3]
];

$response = Http::withToken('your-token')
    ->get('http://your-domain.com/bar-chart/data', $params);

$result = $response->json();
```

---

## 注意事项

1. **认证要求**：接口需要通过auth中间件认证
2. **参数验证**：extra_app_id为必填参数，其他参数为可选
3. **时间格式**：日期时间参数必须使用 Y-m-d H:i:s 格式
4. **数组参数**：数组类型的参数需要传递数组格式
5. **性能考虑**：大数据量查询时建议添加时间范围限制
6. **缓存机制**：相同参数的查询结果会被缓存5-10分钟

---

## 更新日志

- **v1.0** (2025-06-12) - 初始版本发布，提供完整的柱状图数据统计功能
