<?php

/**
 * 对应starRocks数仓中anti_chect_upload_data_primary表
 * @desc 对应starRocks数仓中anti_chect_upload_data_primary表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

class AntiChectUploadDataPrimary extends BaseModel
{
    public const TABLE_NAME = 'anti_chect_upload_data_primary';

    protected $table = self::TABLE_NAME;
}
