<?php

/**
 * 检查黑名单状态的定时脚本
 * @desc 检查黑名单状态的定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/09
 */

namespace App\Console\Commands;

use App\Models\MySQL\Blacklist;
use App\Services\Plugin\BlacklistService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckBlacklistStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:blacklist:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查黑名单有效状态的定时脚本';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("执行检查黑名单有效状态的定时脚本，开始时间：" . now()->toDateTimeString());
        $curTime = date('Y-m-d H:i:s', time());
        Blacklist::query() // 已超过释放时间的白名称 状态修改为无效
            ->where('end_date', '<', $curTime)
            ->where('status', Blacklist::BLACKLIST_STATUS_VALID)
            ->update([
                'status' => Blacklist::BLACKLIST_STATUS_INVALID
            ]);
        // 获取所有状态为失效的黑名单
        Blacklist::query()
            ->where('status', Blacklist::BLACKLIST_STATUS_INVALID)
            ->chunk(1000, function ($blacklists) {
                $blacklistGroup = [];
                // 按效能后台ID分组
                foreach ($blacklists as $blacklist) {
                    $blacklistGroup[$blacklist->developer_app_id][] = $blacklist;
                }
                // 批量删除缓存
                foreach ($blacklistGroup as $developerAppId => $items) {
                    (new BlacklistService(['developer_app_id' => $developerAppId]))->batchDeleteCache($items);
                }
            });
        Log::info("执行检查黑名单有效状态的定时脚本，结束时间：" . now()->toDateTimeString());
    }
}
