<?php

/**
 * 场景数据列表类
 * @desc 场景数据列表类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectClickReportData;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\BaseBuilder;
use Illuminate\Support\Facades\DB;

class SceneListService extends BaseService
{
    /**
     * 获取构建器
     *
     * @return BaseBuilder
     */
    protected function getBuilder()
    {
        $table = AntiChectClickReportData::TABLE_NAME;
        $initTable = AntiChectInit::TABLE_NAME;
        return AntiChectClickReportData::query()
            ->join($initTable, "{$table}.session_id", '=', "{$initTable}.session_id")
            ->where("{$table}.stream_date", '>=', $this->params['start_date'])
            ->where("{$table}.stream_date", '<', $this->params['end_date'])
            ->where("{$initTable}.extra_app_id", '=', $this->params['extra_app_id'])
            ->when($this->params['screen_id'], function ($query, $screenId) use ($table) {
                return $query->where("{$table}.screen_id", $screenId);
            })
            ->whereIn(DB::raw("get_json_string({$table}.game_info, 'account_id')"), $this->params['account_list']);
    }

    /**
     * 获取总数
     *
     * @return int
     */
    public function getCount(): int
    {
        $count = $this->getBuilder()->selectRaw("count(*) as num")->firstFromSR();
        return (int) ($count['num'] ?? 0);
    }

    /**
     * 获取列表
     *
     * @return array
     */
    public function getList(): array
    {
        $table = AntiChectClickReportData::TABLE_NAME;
        $selectRaw = <<<SQL
{$table}.stream_date,
{$table}.server_dev_str,
{$table}.screen_id,
get_json_string({$table}.game_info, 'account_id')  as account_id,
get_json_string({$table}.game_info, 'role_id')     as role_id,
get_json_string({$table}.game_info, 'role_name')   as role_name,
get_json_string({$table}.game_info, 'server_id')   as server_id,
get_json_string({$table}.game_info, 'server_name') as server_name
SQL;
        return $this->getBuilder()
            ->selectRaw($selectRaw)
            ->orderBy('stream_date')
            ->offset(($this->params['page'] - 1) * $this->params['pre_page'])
            ->limit($this->params['pre_page'])
            ->getFromSR();
    }
}
