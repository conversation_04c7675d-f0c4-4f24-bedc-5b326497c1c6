<?php

/**
 * 黑名单服务类
 * @desc 黑名单服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/09
 */

namespace App\Services\Plugin;

use App\Models\MySQL\Blacklist;
use App\Models\MySQL\SwitchConfig;
use App\Models\MySQL\Whitelist;
use App\Models\StarRocks\AntiChectHitLog;
use Illuminate\Support\Facades\Redis;

class BlacklistService extends BaseService
{
    const MESSAGE_TIP = [
        'server_dev_str' => '设备ID',
        'ip' => 'IP',
        'account_id' => '账号ID'
    ];

    /**
     * Redis 连接对象
     *
     * @var Redis
     */
    private $redis;

    /**
     * 初始化
     *
     * @param array $params
     */
    public function __construct(array $params)
    {
        $this->params = $params;
        $this->redis = Redis::connection('api');
    }

    /**
     * 获取黑名单列表
     */
    public function getBlacklist()
    {
        $field = $this->judgeBlacklist($this->params['blacklist_type']);
        $limit = $this->params['per_page'] ?? 10;
        $page = ($this->params['page'] ?? 1) - 1;
        if (empty($field)) {
            return [false, '黑名单列表接口，未匹配到黑名单类型'];
        }
        $builder = Blacklist::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('blacklist_type', $this->params['blacklist_type'])
            ->when(!empty($this->params['search']), function ($query) use ($field) {
                $query->where($field, $this->params['search']);
            })
            // 排除status = ''的情况，isset('') == true，empty(0) == true，无法使用empty
            ->when(isset($this->params['status']) && $this->params['status'] != '', function ($query) {
                $query->where('status', $this->params['status']);
            });
        $total = (clone $builder)->count();
        $data = (clone $builder)
            ->orderBy('created_at', 'desc') // 默认创建时间降序
            ->offset($page < 0 ? 0 : $page * $limit)->limit($limit)
            ->get()->toArray();

        // 获取所有的黑名单类型字段值
        $fieldArray = [];
        // 查询语句
        $selectRaw = '';
        // 循环处理数据
        foreach ($data as $item) {
            // 获取对应的值
            $value = $item[$field];
            // 填充查询的条件
            $fieldArray[] = $value;
            // 拼接查询语句
            $selectRaw .= ",COUNT(CASE WHEN {$field} = '{$value}' AND stream_date >= '{$item['created_at']}' THEN 1 ELSE NULL END) AS hit_count_" . md5($value);
        }
        // 查询数仓计算命中次数
        $result = $data ? AntiChectHitLog::query()
            ->selectRaw(substr($selectRaw, 1))
            ->whereIn($field, array_unique($fieldArray))
            ->where('extra_app_id', $this->params['developer_app_id'])
            ->where('action', SwitchConfig::EXEC_ACTION_IP_BLACKLIST)
            ->groupBy($field)
            ->firstFromSR() : [];
        $data = $this->handleHitCountData($data, $result, $field);
        return [
            'total' => $total,
            'data' => $data,
        ];
    }

    /**
     * 黑名单列表补充命中次数
     * @param $data
     * @param $hitCountArr
     * @param $field
     * @return
     */
    private function handleHitCountData($data, $hitCountArr, $field)
    {
        foreach ($data as $key => $datum) {
            $datum['hit_count'] = intval($hitCountArr["hit_count_" . md5($datum[$field])] ?? 0);
            $data[$key] = $datum;
        }
        return $data;
    }

    /**
     * 添加黑名单
     */
    public function addBlacklist()
    {
        // 判断类型是否正确
        $field = $this->judgeBlacklist($this->params['blacklist_type']);
        if (empty($field)) {
            return [false, '添加黑名单接口，未匹配到黑名单类型'];
        }
        // 判断参数是否为空
        if (empty($this->params[$field])) {
            return [false, sprintf("传入的%s为空！", self::MESSAGE_TIP[$field] ?? '')];
        }
        // 分割参数
        $fieldArray = explode(',', $this->params[$field]);
        // 判断是否存在相同的黑名单
        $count = Blacklist::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('blacklist_type', $this->params['blacklist_type'])
            ->whereIn($field, $fieldArray)->count();
        if ($count != 0) {
            return [false, "黑名单中已存在相同" . self::MESSAGE_TIP[$field] ?? ''];
        }
        // 判断是否存在白名单
        $whiteList = Whitelist::query()->where('developer_app_id', $this->params['developer_app_id'])
            ->where('whitelist_type', $this->params['blacklist_type'])
            ->whereIn($field, $fieldArray)
            ->pluck($field);
        if ($whiteList->isNotEmpty()) {
            return [false, "白名单中已存在相同的：" . implode(',', $whiteList->toArray())];
        }
        // 判断当前黑名单是否有效
        if (strtotime($this->params['start_date']) <= time() && strtotime($this->params['end_date']) >= time()) {
            $this->params['status'] = Blacklist::BLACKLIST_STATUS_VALID;
        } else {
            $this->params['status'] = Blacklist::BLACKLIST_STATUS_INVALID;
        }
        // 组装数据
        $insertData = [];
        foreach ($fieldArray as $value) {
            $insertData[] = [
                'developer_app_id' => $this->params['developer_app_id'],
                $field => $value,
                'status' => $this->params['status'],
                'start_date' => $this->params['start_date'],
                'end_date' => $this->params['end_date'],
                'blacklist_type' => $this->params['blacklist_type'],
                'add_cause' => $this->params['add_cause'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        // 插入到数据库中
        Blacklist::insert($insertData);
        // 判断状态是开启才写入到缓存中
        if ($this->params['status'] == BlackList::BLACKLIST_STATUS_VALID) {
            $redisKey = $this->getRedisKey($this->params['blacklist_type']);
            $this->redis->sAdd($redisKey, ...$fieldArray);
        }
        // 返回结果
        return [true, 'success'];
    }

    /**
     * 编辑黑名单信息
     */
    public function editBlacklist()
    {
        // 判断当前黑名单是否有效
        if (strtotime($this->params['start_date']) <= time() && strtotime($this->params['end_date']) >= time()) {
            $this->params['status'] = Blacklist::BLACKLIST_STATUS_VALID;
        } else {
            $this->params['status'] = Blacklist::BLACKLIST_STATUS_INVALID;
        }
        // 先获取数据
        $blackModel = Blacklist::query()->find($this->params['blacklist_id']);
        // 判断是否存在
        if (empty($blackModel)) {
            return [false, '黑名单不存在'];
        }
        // 移除不需要的参数
        $updateData = $this->params;
        unset($updateData['developer_app_id'], $updateData['whitelist_id']);
        // 更新数据
        $blackModel->update($updateData);
        // 获取下标和redis的key
        $key = $this->judgeBlacklist($blackModel['blacklist_type']);
        $redisKey = $this->getRedisKey($blackModel['blacklist_type']);
        // 如果状态为0要删除缓存，如果为1要添加缓存
        if ($this->params['status'] == Blacklist::BLACKLIST_STATUS_VALID) {
            $this->redis->sAdd($redisKey, $blackModel[$key]);
        } else {
            $this->redis->sRem($redisKey, $blackModel[$key]);
        }
        return [true, 'success'];
    }

    /**
     * 删除黑名单信息
     *
     * @return array
     */
    public function deleteBlacklist()
    {
        // 获取数据
        $list = Blacklist::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->whereIn('blacklist_id', explode(',', $this->params['blacklist_id']))
            ->get();
        // 删除数据库数据
        Blacklist::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->whereIn('blacklist_id', explode(',', $this->params['blacklist_id']))
            ->delete();
        // 删除redis数据
        $this->batchDeleteCache($list);
        // 返回结果
        return [true, 'success'];
    }

    /**
     * 黑名单类型判断
     */
    private function judgeBlacklist($type)
    {
        switch ($type) {
            case Blacklist::BLACKLIST_TYPE_SERVICE_DEV_STR:
                $field = 'server_dev_str';
                break;
            case Blacklist::BLACKLIST_TYPE_IP:
                $field = 'ip';
                break;
            case Blacklist::BLACKLIST_TYPE_ACCOUNT_ID:
                $field = 'account_id';
                break;
            default:
                $field = '';
        }
        return $field;
    }

    /**
     * 获取Redis的key
     *
     * @param  int $type 黑名单类型
     * @return string
     */
    private function getRedisKey($type)
    {
        return 'plugin:black:' . $this->judgeBlacklist($type) . ":{$this->params['developer_app_id']}";
    }

    /**
     * 批量删除缓存
     *
     * @param  array $list 数据列表
     * @return void
     */
    public function batchDeleteCache($list)
    {
        // 根据不同的类型组装不同的数据
        $deleteData = [];
        foreach ($list as $item) {
            $key = $this->judgeBlacklist($item['blacklist_type']);
            $deleteData[$item['blacklist_type']][] = $item[$key];
        }
        // 删除redis数据
        foreach ($deleteData as $key => $value) {
            $redisKey = $this->getRedisKey($key);
            $this->redis->sRem($redisKey, ...$value);
        }
    }
}
