<?php

/**
 * 同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_pack表脚本
 * @desc 同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_pack表脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/17
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Services\StarRocksService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncPrimaryPackDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:primary:pack {time? : 时间}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_pack表脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        $time = ($this->argument('time') ?? Carbon::now()->subMinutes(5)->toDateTimeString());

        try {
            // sql语句
            $sql = <<<SQL
insert into anti_chect_upload_data_primary_pack
select * from (select session_id,
       stream_date,
       server_dev_str,
       get_json_string(unnest, 'app_packageName') AS app_package,
       get_json_string(unnest, 'app_name')        AS app_name
from (select *
      from (select session_id,
                   server_dev_str,
                   stream_date,
                   SPLIT(REGEXP_REPLACE(REGEXP_REPLACE(application_info, '\\\[|\\\]', ''), '},{', '}||{'),
                         '||') AS application_info
            from `anti_chect_upload_data_primary` where stream_date >= '{$time}')  AS t) AS t, UNNEST(`application_info`)) as t
where null_or_empty(app_package) = false
SQL;
            // 同步数据表
            (new StarRocksService())->execute($sql);
            //打印日志
            Log::info("执行同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_pack表脚本完成, dateTime: {$time}, lastTime: " . date('Y-m-d H:i:s'));
        } catch (\Exception $e) {
            Log::error("执行同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_pack表脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
