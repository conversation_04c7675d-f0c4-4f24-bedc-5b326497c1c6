<?php

/**
 * 柱状图数据服务类
 * @desc 柱状图数据服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/12
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;
use Illuminate\Support\Facades\Log;
use Exception;

class BarChartService extends BaseService
{
    /**
     * 获取柱状图数据
     *
     * 主要包含以下数据：
     * 1. 风险等级分布统计（低、中、高风险）
     * 2. 游戏上传类型分布（正常上传、重签包、AI检测）
     * 3. Root状态分布（已Root、未Root）
     * 4. 重签包名统计
     *
     * @return array 格式化后的柱状图数据
     * @throws Exception 当数据获取失败时抛出异常
     */
    public function getData(): array
    {
        try {
            // 获取风险等级和游戏上传类型统计数据
            $statisticsData = $this->getStatisticsData();
            // 获取重签包名统计数据
            $signPackageData = $this->getSignPackageStatistics();
            // 格式化数据
            $result = [
                'risk_and_upload_data' => [
                    'risk_level' => $this->formatRiskLevelData($statisticsData),
                    'upload_type' => $this->formatUploadTypeData($statisticsData),
                    'root_status' => $this->formatRootStatusData($statisticsData),
                ],
                'sign_package_count' => $signPackageData
            ];
            // 返回结果
            return $result;
        } catch (Exception $e) {
            Log::error('BarChartService::getData 获取柱状图数据失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine() . ' params: ' . json_encode($this->params));
            throw new Exception('获取柱状图数据失败：' . $e->getMessage());
        }
    }

    /**
     * 格式化风险等级数据
     *
     * @param array $data 原始统计数据
     * @return array 格式化后的风险等级数据
     */
    private function formatRiskLevelData(array $data): array
    {
        return [
            'not_risk' => $data['risk_level_0_count'] ?? 0,      // 没风险数量
            'low_risk' => $data['risk_level_1_count'] ?? 0,      // 低风险数量
            'medium_risk' => $data['risk_level_2_count'] ?? 0,   // 中风险数量
            'high_risk' => $data['risk_level_3_count'] ?? 0,     // 高风险数量
        ];
    }

    /**
     * 格式化游戏上传类型数据
     *
     * @param array $data 原始统计数据
     * @return array 格式化后的上传类型数据
     */
    private function formatUploadTypeData(array $data): array
    {
        return [
            'game_upload' => $data['is_game_upload_1_count'] ?? 0,  // 正常游戏上传数量
            'resign_count' => $data['is_game_upload_2_count'] ?? 0, // 重签包上传数量
            'ai_count' => $data['is_game_upload_3_count'] ?? 0,     // AI检测上传数量
        ];
    }

    /**
     * 格式化Root状态数据
     *
     * @param array $data 原始统计数据
     * @return array 格式化后的Root状态数据
     */
    private function formatRootStatusData(array $data): array
    {
        return [
            'is_root' => $data['is_root_count'] ?? 0,      // 已Root设备数量
            'not_root' => $data['is_not_root_count'] ?? 0, // 未Root设备数量
        ];
    }

    /**
     * 获取重签包名统计数据
     *
     * 统计所有重签包的包名及其出现次数，按出现次数降序排列
     * 只统计is_game_upload=2（重签包）且hitbug_explain_desc不为空的记录
     *
     * @return array 重签包名统计数据，格式：[['name' => '包名', 'num' => 数量], ...]
     * @throws Exception 当查询失败时抛出异常
     */
    public function getSignPackageStatistics(): array
    {
        try {
            $builder = $this->getInitJoinPrimaryBuilder();
            $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;

            // 构建查询SQL：统计重签包名及其数量
            $selectRaw = <<<SQL
{$table}.hitbug_explain_desc as name,
COUNT(distinct {$table}.server_dev_str) as num
SQL;
            $result = $builder->selectRaw($selectRaw)
                ->where($table . '.is_game_upload', 2) // 只查询重签包类型
                ->where($table . '.hitbug_explain_desc', '!=', '')             // 排除空包名
                ->groupBy($table . '.hitbug_explain_desc')                     // 按包名分组
                ->orderBy('num', 'desc')                                       // 按数量降序排列
                ->getFromSR();

            return $result;
        } catch (Exception $e) {
            Log::error('BarChartService::getSignPackageStatistics 获取重签包统计数据失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            throw new Exception('获取重签包统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取统计数据（风险等级、游戏上传类型、Root状态分布）
     *
     * 通过一次SQL查询获取所有需要的统计数据：
     * 1. 风险等级分布（低、中、高风险）
     * 2. 游戏上传类型分布（正常上传、重签包、AI检测）
     * 3. Root状态分布（已Root、未Root）
     *
     * @return array 包含各种统计数据的数组
     * @throws Exception 当查询失败时抛出异常
     */
    public function getStatisticsData(): array
    {
        try {

            $builder = $this->getInitJoinPrimaryBuilder();
            $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
            $initTable = AntiChectInit::TABLE_NAME;

            // 构建统计查询SQL，使用CASE WHEN进行条件统计
            $selectRaw = $this->buildStatisticsSelectSql($table, $initTable);

            $result = $builder->selectRaw($selectRaw)->firstFromSR();

            return $result;
        } catch (Exception $e) {
            Log::error('BarChartService::getStatisticsData 获取统计数据失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            throw new Exception('获取统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 构建统计查询的SELECT SQL语句
     *
     * @param string $table 主表名
     * @param string $initTable 初始化表名
     * @return string SQL查询语句
     */
    private function buildStatisticsSelectSql(string $table, string $initTable): string
    {
        return <<<SQL
SUM(CASE WHEN {$table}.risk_level = 0 THEN 1 ELSE 0 END) AS risk_level_0_count,
SUM(CASE WHEN {$table}.risk_level = 1 THEN 1 ELSE 0 END) AS risk_level_1_count,
SUM(CASE WHEN {$table}.risk_level = 2 THEN 1 ELSE 0 END) AS risk_level_2_count,
SUM(CASE WHEN {$table}.risk_level = 3 THEN 1 ELSE 0 END) AS risk_level_3_count,
SUM(CASE WHEN {$table}.is_game_upload IS NULL OR {$table}.is_game_upload = 0 THEN 1 ELSE 0 END) AS is_game_upload_null_or_0_count,
SUM(CASE WHEN {$table}.is_game_upload = 1 THEN 1 ELSE 0 END) AS is_game_upload_1_count,
SUM(CASE WHEN {$table}.is_game_upload = 2 THEN 1 ELSE 0 END) AS is_game_upload_2_count,
SUM(CASE WHEN {$table}.is_game_upload = 3 THEN 1 ELSE 0 END) AS is_game_upload_3_count,
SUM(CASE WHEN {$initTable}.is_root = 1 THEN 1 ELSE 0 END) AS is_root_count,
SUM(CASE WHEN {$initTable}.is_root = 0 THEN 1 ELSE 0 END) AS is_not_root_count
SQL;
    }
}
