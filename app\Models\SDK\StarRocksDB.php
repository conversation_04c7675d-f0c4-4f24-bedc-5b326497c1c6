<?php

/**
 * 调用starRocks查询的DB基础类
 * @desc 调用starRocks查询的DB基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\SDK;

use App\Services\SdkStarRocksService;
use Illuminate\Database\Query\Builder;

class StarRocksDB
{
    /**
     * starRocks服务
     *
     * @var StarRocksService
     */
    protected $starRocks;

    /**
     * 构造器
     *
     * @var Builder
     */
    protected $builder;

    /**
     * 构造函数
     *
     * @param Builder $builder
     * @param bool $initStarRocks
     */
    public function __construct(Builder $builder, bool $initStarRocks = true)
    {
        // 初始化starRocks服务
        if ($initStarRocks) {
            $this->starRocks = new SdkStarRocksService();
        }
        $this->builder = $builder;
    }

    /**
     * 获取SQL
     *
     * @param Builder $builder
     * @return string
     */
    public static function toSql(Builder $builder): string
    {
        return (new static($builder, false))->getSqlBindings();
    }

    /**
     * 解析sql
     *
     * @return string
     */
    protected function getSqlBindings(): string
    {
        $queryBindings = $this->builder->getBindings();
        foreach ($queryBindings as $key => $val) {
            if (!is_int($val) && !is_float($val)) {
                $val = "'" . trim($val) . "'";
            }
            $queryBindings[$key] = $val;
        }
        // 因为%使用vsprintf会有问题, 所以需要转义
        $sql = str_replace('%', '%%', $this->builder->toSql());
        // 替换?为%s
        $tmp = str_replace('?', '%s', $sql);
        //返回sql
        return vsprintf($tmp, $queryBindings);
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function get(): array
    {
        $sql = $this->getSqlBindings();
        return $this->starRocks->query($sql);
    }

    /**
     * 创建查询
     *
     * @param Builder $builder
     * @return StarRocksDB
     */
    public static function query(Builder $builder): StarRocksDB
    {
        return new static($builder);
    }

    /**
     * 获取第一条数据
     *
     * @return array
     */
    public function first(): array
    {
        $sql = $this->getSqlBindings();
        $res = $this->starRocks->query($sql);
        return $res[0] ?? [];
    }
}
