<?php

/**
 * 检查外挂状态的定时脚本
 * @desc 检查外挂状态的定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/13
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Jobs\CheckPluginStatusJob;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class CheckPluginStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:plugin:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查外挂状态的定时脚本';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        //获取任务ID
        $uuid = Str::uuid();
        //打印日志
        Log::info("执行检查外挂状态的定时脚本，uuid值：{$uuid}，开始时间：" . now()->toDateTimeString());
        //分发任务
        CheckPluginStatusJob::dispatch(now()->subSeconds(120)->toDateTimeString())->onQueue('check_plugin_status');
        //延迟20秒任务
        CheckPluginStatusJob::dispatch(now()->subSeconds(100)->toDateTimeString())->delay(now()->addSeconds(20))->onQueue('check_plugin_status');
        //延迟40秒任务
        CheckPluginStatusJob::dispatch(now()->subSeconds(80)->toDateTimeString())->delay(now()->addSeconds(40))->onQueue('check_plugin_status');
        //打印日志
        Log::info("执行检查外挂状态的定时脚本，uuid值：{$uuid}，结束时间：" . now()->toDateTimeString());
    }
}
