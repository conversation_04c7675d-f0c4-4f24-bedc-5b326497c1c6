<?php

/**
 * 检查白名单状态的定时脚本
 * @desc 检查白名单状态的定时脚本
 * <AUTHOR> <EMAIL>
 * @date 2023/12/27
 */

namespace App\Console\Commands;

use App\Models\MySQL\Whitelist;
use App\Services\Plugin\WhitelistService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckWhitelistStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:whitelist:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查白名单有效状态的定时脚本';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("执行检查白名单有效状态的定时脚本，开始时间：" . now()->toDateTimeString());
        $curTime = date('Y-m-d H:i:s', time());
        Whitelist::query() // 已超过释放时间的白名称 状态修改为无效
            ->where('end_date', '<', $curTime)
            ->where('status', Whitelist::WHITELIST_STATUS_VALID)
            ->update([
                'status' => Whitelist::WHITELIST_STATUS_INVALID
            ]);
        // 获取所有状态为失效的白名单
        Whitelist::query()
            ->where('status', Whitelist::WHITELIST_STATUS_INVALID)
            ->chunk(1000, function ($whitelists) {
                $whitelistGroup = [];
                // 按效能后台ID分组
                foreach ($whitelists as $whitelist) {
                    $whitelistGroup[$whitelist->developer_app_id][] = $whitelist;
                }
                // 批量删除缓存
                foreach ($whitelistGroup as $developerAppId => $items) {
                    (new WhitelistService(['developer_app_id' => $developerAppId]))->batchDeleteCache($items);
                }
            });
        Log::info("执行检查白名单有效状态的定时脚本，结束时间：" . now()->toDateTimeString());
    }
}
