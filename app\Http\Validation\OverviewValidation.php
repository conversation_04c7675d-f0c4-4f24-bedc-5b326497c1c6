<?php

/**
 * 录屏概览校验类
 * @desc 录屏概览校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

/**
 * @method static OverviewValidation build()
 */
class OverviewValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function extraAppId(): OverviewValidation
    {
        $this->rules['extra_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 开始时间
     *
     * @return $this
     */
    public function startDate(): OverviewValidation
    {
        $this->rules['start_date'] = 'date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 结束时间
     *
     * @return $this
     */
    public function endDate(): OverviewValidation
    {
        $this->rules['end_date'] = 'date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 平台
     *
     * @return $this
     */
    public function osType(): OverviewValidation
    {
        $this->rules['os_type'] = 'integer';
        return $this;
    }
}
