<?php

use App\Http\Controllers\ApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['prefix' => 'plugin'], function () {
    Route::get('/check', [ApiController::class, 'check']);
    Route::get('/list', [ApiController::class, 'list']);
    Route::get('/scene', [ApiController::class, 'scene']);
});
