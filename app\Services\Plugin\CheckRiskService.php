<?php

/**
 * 外挂检测数据检测风险类
 * @desc 外挂检测数据检测风险类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/12/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

class CheckRiskService extends BaseService
{
    /**
     * 等级
     *
     * @var integer
     */
    private $level = 0;

    /**
     * 获取风险等级
     *
     * @return integer
     */
    public function getLevel()
    {
        // 判断risk_level是否存在
        if (!empty($this->params['risk_level'])) {
            // 将数组反转
            $map = array_flip(static::RISK_LEVEL_MAP);
            // 获取风险等级
            $this->level = $map[$this->params['risk_level']] ?? 0;
        }
        // 判断风险等级是否为空
        if (empty($this->level)) {
            $this->checkLowLevel();
        }
        return $this->level;
    }

    /**
     * 检测低等级
     *
     * @return void
     */
    private function checkLowLevel()
    {
        //判断字符串中是否包含crwxrwxrwx
        if (strpos($this->params['write_info'] ?? '', 'crwxrwxrwx') !== false) {
            $this->level = static::LOW_RISK;
            return;
        }
        //判断字符串中是否包含crw-rwxrwx
        if (strpos($this->params['write_info'] ?? '', 'crw-rwxrwx') !== false) {
            $this->level = static::LOW_RISK;
            return;
        }
        //判断屏幕分辨率720*1280
        if (($this->params['resolution'] ?? '') == '720*1280') {
            $this->level = static::LOW_RISK;
            return;
        }
        //判断屏幕像素密度是否320
        if (($this->params['screen_density_dpi'] ?? '') == '320') {
            $this->level = static::LOW_RISK;
            return;
        }
        //判断手机是否root
        if (($this->params['is_root'] ?? '') == '1') {
            $this->level = static::LOW_RISK;
            return;
        }
        // 是否模拟器
        if (($this->params['is_emulator'] ?? '') == '1') {
            $this->level = static::LOW_RISK;
            return;
        }
    }
}
