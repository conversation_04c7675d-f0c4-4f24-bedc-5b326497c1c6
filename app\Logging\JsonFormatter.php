<?php

namespace App\Logging;

use Monolog\Formatter\JsonFormatter as BaseJsonFormatter;

class J<PERSON>Formatter extends BaseJsonFormatter
{
    public function format(array $record): string
    {
        //获取ISO8601标准时间格式 并精确到毫秒 2020-02-29T12:11:11.247+00:00
        //写法一 必须支持RFC3339扩展
        $timestamp = $record['datetime']->format(DATE_RFC3339_EXTENDED);
        //写法二
        //$millisecond = substr($record['datetime']->format("u"), 0, 3);
        //$timestamp = $record['datetime']->format('Y-m-d\TH:i:s.') . $millisecond . $record['datetime']->format('P');
        $bizId = $record['context']['biz_id'] ?? '';
        //用请求的url作为业务标识
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        if (empty($bizId) && !empty($requestUri)) {
            if (strpos($requestUri, '?') !== false) {
                $bizId = substr($requestUri, 0, strpos($requestUri, '?'));
            }
        }
        $traceId = $record['context']['trace_id'] ?? '';
        $userId = $record['context']['user_id'] ?? '';
        $newRecord = [
            '@timestamp' => $timestamp,
            'level_name' => $record['level_name'], //日志级别
            'trace_id' => $traceId, //调用链标识
            'biz_id' => $bizId, //业务标识
            'user_id' => $userId, //用户标识
            'message' => $record['message'],
        ];
        return $this->toJson($this->normalize($newRecord), true) . ($this->appendNewline ? "\n" : '');
    }
}
