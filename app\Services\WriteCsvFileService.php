<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class WriteCsvFileService
{
    /**
     * 待写入的数据
     *
     * @var array
     */
    protected $data;

    /**
     * 文件名
     *
     * @var string
     */
    protected $fileName;

    /**
     * 同步的字段
     *
     * @var array
     */
    protected $columns;

    /**
     * 构造函数
     *
     * @param $fileName
     * @param $columns
     * @param $data
     */
    public function __construct($fileName, $columns, $data)
    {
        $this->fileName = $fileName;
        $this->data = $data;
        $this->columns = $columns;
    }

    /**
     * 写入文件
     *
     * @return string
     */
    public function write(): string
    {
        $path = storage_path($this->fileName);
        //判断目录是否存在 不存在就创建
        $dir = pathinfo($path)['dirname'];
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        $out = fopen($path, "w");
        Log::info('开始写入csv文件，文件名：' . $path);
        foreach ($this->data as $row) {
            $content = [];
            foreach ($this->columns as $column) {
                $content[] = $row[$column] ?? '';
            }
            $lastIndex = count($content) - 1;
            $content[$lastIndex] = $content[$lastIndex] . "\n";
            fwrite($out, implode("|$|$|", $content));
        }
        fclose($out);
        Log::info('写入csv文件结束，文件名：' . $path);
        return $path;
    }
}
