<?php

/**
 * 外挂检测报告搜索下拉
 * @desc 外挂检测报告搜索下拉
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

class SearchService extends BaseService
{
    /**
     * 要获取的字段数组
     *
     * @var string[]
     */
    public const COLUMNS = [
        'sdk_ver',
        'app_version',
    ];

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $data = [];
        $builder = $this->getBuilder();
        foreach (self::COLUMNS as $column) {
            $dbData = (clone $builder)->select([$column])
                ->where($column, '!=', '')
                ->groupBy($column)
                ->getFromSR();
            $data[$column] = $this->format(array_column($dbData, $column));
        }
        return $data;
    }

    /**
     * 格式化数据
     *
     * @param array $data
     * @return array
     */
    private function format(array $data): array
    {
        return array_map(function ($item) {
            return [
                'label' => $item,
                'value' => $item,
            ];
        }, $data);
    }
}
