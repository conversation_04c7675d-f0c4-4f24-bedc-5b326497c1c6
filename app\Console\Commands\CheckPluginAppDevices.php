<?php

/**
 * 检查新增外挂应用定时脚本
 * @desc 检查新增外挂应用定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/22
 */

namespace App\Console\Commands;

use App\Models\StarRocks\AntiChectHitLog;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Services\Plugin\OverviewService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckPluginAppDevices extends Command
{
    /**
     * 机器人webhook地址
     *
     * @var string
     */
    const HOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f920047b-86f4-4e61-8253-24d1f990b03e';

    /**
     * 效能应用列表
     *
     * @var array
     */
    const APP_LIST = [
        6 => '长安幻想',
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:plugin:app:devices {appId : 应用ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查新增外挂应用定时脚本';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("执行检查新增外挂应用定时脚本，开始时间：" . now()->toDateTimeString());
        // 获取应用ID
        $appId = $this->argument('appId');
        // 获取应用名称
        $appName = static::APP_LIST[$appId] ?? null;
        // 判断应用是否为空，如果为空则不执行，并记录日志
        if (empty($appName)) {
            Log::error("应用ID：{$appId} 不存在，请检查输入参数是否正确！");
            return;
        }
        // 获取昨天时间
        $yesterday = now()->subDay()->toDateString();
        // 发送请求的内容
        $content = <<<CONTENT
# **<font color="info">【{$appName}】</font> <font color="blue">{$yesterday}</font> 命中外挂检测，应用列表中，并没已知外挂包名的设备**

CONTENT;
        // 查询数据库中符合的设备
        $list = AntiChectInit::query()
            ->selectRaw(AntiChectInit::TABLE_NAME . '.server_dev_str')
            ->join(AntiChectHitLog::TABLE_NAME, AntiChectHitLog::TABLE_NAME . '.session_id', '=', AntiChectInit::TABLE_NAME . '.session_id')
            ->join(AntiChectUploadDataPrimary::TABLE_NAME, AntiChectUploadDataPrimary::TABLE_NAME . '.session_id', '=', AntiChectInit::TABLE_NAME . '.session_id')
            ->where(AntiChectInit::TABLE_NAME . '.stream_date', '>=', "{$yesterday} 00:00:00")
            ->where(AntiChectInit::TABLE_NAME . '.stream_date', '<=', "{$yesterday} 23:59:59")
            ->where(function ($query) {
                foreach (OverviewService::CHECT_APP_LIST as $packageName) {
                    $query->whereRaw(AntiChectUploadDataPrimary::TABLE_NAME . '.application_info NOT LIKE ' . '\'%"app_packageName":"' . $packageName . '"%\'');
                }
                foreach (OverviewService::CHECT_SPECIAL_APP_LIST as $packageName) {
                    $query->whereRaw(AntiChectUploadDataPrimary::TABLE_NAME . '.application_info NOT LIKE ' . '\'%"app_packageName":"' . $packageName . '"%\'');
                }
            })
            ->groupBy(AntiChectInit::TABLE_NAME . '.server_dev_str')
            ->getFromSR();
        // 判断是否有设备
        if (empty($list)) {
            Log::info("应用：{$appName}，昨天没有新增外挂设备！");
            return;
        }
        // 遍历设备列表，并添加到内容中
        foreach ($list as $device) {
            $content .= ">{$device['server_dev_str']}
";
        }
        // 推动到机器人
        Http::asJson()->post(static::HOOK_URL, [
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content
            ]
        ]);
        // 结束
        Log::info("执行检查新增外挂应用定时脚本，结束时间：" . now()->toDateTimeString());
    }
}
