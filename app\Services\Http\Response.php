<?php

/**
 * 响应工具
 * @desc 响应工具
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/11
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Http;

trait Response
{
    /**
     * 成功返回
     *
     * @param array $data
     * @param string $message
     * @return array
     */
    protected function success(array $data = [], string $message = 'success'): array
    {
        return [
            'code' => 0,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 失败返回
     *
     * @param int $code
     * @param string $message
     * @param array $data
     * @return array
     */
    protected function fail(int $code, string $message = 'fail', array $data = []): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }
}
