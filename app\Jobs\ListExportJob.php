<?php

/**
 * 列表导出队列任务
 * @desc 列表导出队列任务
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs;

use App\Services\Plugin\ListService;
use App\Utils\RedisKey;
use Box\Spout\Common\Entity\Style\CellAlignment;
use Box\Spout\Common\Exception\InvalidArgumentException;
use Box\Spout\Common\Exception\IOException;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Writer\Exception\WriterNotOpenedException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;

class ListExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 设置最大尝试次数
     *
     * @var int
     */
    public $tries = 1;

    /**
     * 在超时之前任务可以运行的秒数
     *
     * @var int
     */
    public $timeout = 3600;

    /**
     * 获取条数
     *
     * @var int
     */
    const LIMIT = 500;

    /**
     * 任务ID
     *
     * @var string
     */
    private $taskId;

    /**
     * 筛选参数
     *
     * @var array
     */
    private $params = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($taskId, $params)
    {
        $this->taskId = $taskId;
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws IOException
     * @throws InvalidArgumentException
     * @throws WriterNotOpenedException
     */
    public function handle()
    {
        //设置内存大小
        ini_set('memory_limit', '2048M');
        // 修改获取条数
        $this->params['pre_page'] = self::LIMIT;
        // 设置页码
        $this->params['page'] = 1;
        // 获取redis的key
        $redisKey = RedisKey::getPluginExportDataKey($this->taskId);
        // 实例化服务类
        $service = new ListService($this->params);
        // 获取记录总数
        $total = $service->getCount();
        // 判断是否有数据
        if (empty($total)) {
            // 设置错误信息
            Redis::setEx($redisKey, $this->timeout, json_encode(['code' => 1, 'msg' => '没有数据可以导出~']));
            // 中断
            return;
        }
        // 初始化writer
        $writer = WriterEntityFactory::createXLSXWriter();
        // 设置默认样式
        $writer->setDefaultRowStyle((new StyleBuilder())
            ->setCellAlignment(CellAlignment::CENTER)
            ->build());
        // 文件名
        $fileName = "app/export/外挂数据-{$this->taskId}.xlsx";
        // 设置文件名
        $writer->openToFile(storage_path($fileName));
        // 设置表头
        $row = WriterEntityFactory::createRowFromArray([
            '启动时间',
            '设备ID',
            '平台类型',
            '账号ID',
            '服务器ID',
            '服务器名称',
            '角色ID',
            '角色名称',
            '风险标签',
            '风险等级',
            '设备类型',
            '应用包名',
            '应用版本',
            '使用外挂时长',
        ], (new StyleBuilder())
            ->setFontBold()
            ->setFontSize(14)
            ->setBackgroundColor('00B0F0')
            ->build());
        $writer->addRow($row);
        // 当前数量
        $currentCount = 0;
        // 循环获取数据
        while (true) {
            // 获取数据
            $list = $service->getExportList();
            // 判断是否还有数据
            if (empty($list)) {
                break;
            }
            // 写入数据
            foreach ($list as $item) {
                unset($item['session_id']);
                $writer->addRow(WriterEntityFactory::createRowFromArray(array_values($item)));
            }
            // 设置当前数量
            $currentCount += count($list);
            // 设置导出进度
            $progress = intval(($currentCount / $total) * 100);
            // 判断进度值，进度值少于100时，才设置redis
            if ($progress < 100) {
                Redis::setEx($redisKey, $this->timeout, json_encode([
                    'code' => 0,
                    'progress' => $progress,
                    'msg' => '导出中~'
                ]));
            }
            // 判断是否还有数据
            if (count($list) < self::LIMIT) {
                break;
            }
            // 下一页
            $this->params['page']++;
            // 设置分页
            $service->setPage($this->params['page']);
        }
        // 关闭writer
        $writer->close();
        // 设置完成导出
        Redis::setEx($redisKey, $this->timeout, json_encode(['code' => 0, 'progress' => 100, 'path' => $fileName, 'msg' => '导出成功~']));
    }
}
