<?php

/**
 * 验证器基础类
 * @desc 验证器基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

abstract class BaseValidation
{
    /**
     * 请求对象
     *
     * @var Request
     */
    protected $request;

    /**
     * 规则
     *
     * @var array
     */
    protected $rules = [];

    /**
     * A字段为A值，B字段需要有校验规则
     * 使用方法
     * $sometimeRules[待校验字段] = ["规则", "规则校验匿名函数"]
     * 如：
     * $sometimeRules['popup_content'] = ['required', function ($input) { return $input->exec_action == 2; }]
     * @var array
     */
    protected $sometimeRules = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->request = request();
    }

    /**
     * 静态创建方法
     *
     * @return BaseValidation
     */
    public static function build(): BaseValidation
    {
        return new static();
    }

    /**
     * 校验
     *
     * @param array $rules
     * @return array
     * @throws ValidationException
     */
    public function validate(array $rules = []): array
    {
        //获取请求参数
        $params = $this->request->all();
        //合并规则
        $rules = array_merge($this->rules, $rules);
        //提示信息
        $messages = $this->messages();
        //校验
        $validator = Validator::make($params, $rules, $messages);

        // 新增校验规则
        $sometimeRules = $this->sometimeRules;
        if (!empty($sometimeRules)) {
            foreach ($sometimeRules as $k => $v) {
                $stRule = $v[0];
                $stFunc = $v[1];
                $validator->sometimes($k, $stRule, $stFunc);
            }
        }

        //校验失败，抛出异常
        if ($validator->fails()) {
            $this->exception($validator->errors()->first());
        }
        //校验成功，返回校验后的数据
        return $validator->validated();
    }

    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [];
    }

    /**
     * 抛出异常
     *
     * @param $message
     */
    protected function exception($message)
    {
        $code = Response::HTTP_UNPROCESSABLE_ENTITY;
        $message = $message ?? "表单数据错误";
        $data = [];
        throw new HttpResponseException(response()->json(compact('code', 'message', 'data'), Response::HTTP_OK));
    }
}
