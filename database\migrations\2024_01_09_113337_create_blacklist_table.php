<?php

/**
 * 黑名单数据库迁移文件
 * @desc 黑名单数据库迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/09
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlacklistTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('external_plugin')->create('blacklist', function (Blueprint $table) {
            $table->bigIncrements('blacklist_id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('server_dev_str', 256)->default('')->comment('设备ID');
            $table->string('ip', 64)->default('')->comment('IP');
            $table->string('account_id', 64)->default('')->comment('账号ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('黑名单状态，0：无效，1：有效');
            $table->unsignedTinyInteger('blacklist_type')->default(0)->comment('黑名单类型，1：设备，2：IP，3：账号');
            $table->dateTime('start_date')->nullable()->comment('黑名单有效开始时间');
            $table->dateTime('end_date')->nullable()->comment('黑名单有效结束时间（黑名单到期时间）');
            $table->text('add_cause')->nullable()->comment('添加原因');
            $table->softDeletes(); // 软删除字段
            $table->timestamps();
            $table->index('blacklist_type');
            $table->index('server_dev_str');
            $table->index('ip');
            $table->index('account_id');
            $table->index('end_date');
        });
        \DB::connection('external_plugin')->statement("ALTER TABLE `blacklist` comment '黑名单管理表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('external_plugin')->dropIfExists('blacklist');
    }
}
