<?php

/**
 * 外挂检测数据处理基础类
 * @desc 外挂检测数据处理基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\MySQL\SwitchConfig;
use App\Models\StarRocks\AntiChectHitLog;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;
use App\Models\StarRocks\BaseBuilder;
use App\Models\StarRocks\StarRocksDB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

abstract class BaseService
{
    /**
     * 高风险
     *
     * @var int
     */
    const HIGH_RISK = 1;

    /**
     * 中风险
     *
     * @var int
     */
    const MIDDLE_RISK = 2;

    /**
     * 低风险
     *
     * @var int
     */
    const LOW_RISK = 3;

    /**
     * 风险等级转换
     *
     * @var array
     */
    const RISK_LEVEL_MAP = [
        self::HIGH_RISK => 3,
        self::MIDDLE_RISK => 2,
        self::LOW_RISK => 1,
    ];

    /**
     * 过滤条件方法
     *
     * @var array
     */
    public const WHERE_METHOD = [
        'start_date' => 'dateWhere',
        'end_date' => 'dateWhere',
        'account_id' => 'accountIdWhere',
        'server_id' => 'serverIdWhere',
        'role_id' => 'roleIdWhere',
        'role_name' => 'roleNameWhere',
        'action' => 'actionWhere',
        'risk_level' => 'riskLevelWhere',
        'ip_address' => 'ipAddressWhere',
        'report_type' => 'reportTypeWhere',
    ];

    /**
     * 排除的参数
     *
     * @var array
     */
    public const EXCLUDE_PARAMS = [
        'page',
        'pre_page',
    ];

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 初始化
     *
     * @param array $params
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据库Builder
     *
     * @return BaseBuilder
     */
    protected function getBuilder()
    {
        return AntiChectInit::query()
            ->join(AntiChectUploadDataPrimaryV2::TABLE_NAME, AntiChectInit::TABLE_NAME . '.session_id', '=', AntiChectUploadDataPrimaryV2::TABLE_NAME . '.session_id')
            ->where(function (Builder $query) {
                foreach ($this->params as $key => $value) {
                    //判断key是否要排除
                    if (in_array($key, self::EXCLUDE_PARAMS, true)) {
                        continue;
                    }
                    //获取自定义的过滤方法
                    $method = self::WHERE_METHOD[$key] ?? null;
                    //判断是否有自定义的过滤方法
                    if (!is_null($method) && (!empty($value) || $key == 'report_type')) {
                        $this->$method($query, $key, $value);
                    } elseif (!empty($value) && is_array($value)) { //判断是否数组
                        $query->whereIn("{$query->getModel()->getTable()}.{$key}", $value);
                    } elseif (!empty($value)) { //值不为空
                        $query->where("{$query->getModel()->getTable()}.{$key}", $value);
                    }
                }
                return $query;
            });
    }

    /**
     * 获取数据库Builder（init表 join hit表）
     * @return \Illuminate\Database\Query\Builder
     */
    protected function getInitJoinHitBuilder()
    {
        $tableA = AntiChectInit::TABLE_NAME;
        $tableB = AntiChectHitLog::TABLE_NAME;
        $obj = AntiChectInit::query()
            ->join($tableB, "{$tableA}.session_id", '=', "{$tableB}.session_id")
            ->where(function (Builder $query) {
                return $this->whereBuilder($query, $this->params);
            });

        return $obj;
    }

    /**
     * 获取数据库Builder（init表 join primary表）
     * @return \Illuminate\Database\Query\Builder
     */
    protected function getInitJoinPrimaryBuilder()
    {
        $tableA = AntiChectInit::TABLE_NAME;
        $tableC = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $obj = AntiChectInit::query()
            ->join($tableC, "{$tableA}.session_id", '=', "{$tableC}.session_id")
            ->where(function (Builder $query) {
                return $this->whereBuilder($query, $this->params);
            });
        return $obj;
    }

    /**
     * 获取数据库Builder（init表 join hit表 join primary表）
     * @return \Illuminate\Database\Query\Builder
     */
    protected function getInitJoinHitAndPrimaryBuilder()
    {
        $tableA = AntiChectInit::TABLE_NAME;
        $tableB = AntiChectHitLog::TABLE_NAME;
        $tableC = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $obj = AntiChectInit::query()
            ->join($tableB, "{$tableA}.session_id", '=', "{$tableB}.session_id")
            ->join($tableC, "{$tableA}.session_id", '=', "{$tableC}.session_id")
            ->where(function (Builder $query) {
                return $this->whereBuilder($query, $this->params);
            });

        return $obj;
    }

    /**
     * 获取数据库Builder（init表）
     * @return BaseBuilder
     */
    protected function getInitBuilder()
    {
        // var_dump(AntiChectInit::query()->getConnection()->getDatabaseName());die;
        $obj = AntiChectInit::query()
            ->where(function (Builder $query) {
                return $this->whereBuilder($query, $this->params);
            });

        return $obj;
    }

    /**
     * 参数组装过滤条件
     * @param $query
     * @param $params
     * @return mixed
     */
    protected function whereBuilder($query, $params)
    {
        foreach ($params as $key => $value) {
            //判断key是否要排除
            if (in_array($key, self::EXCLUDE_PARAMS, true)) {
                continue;
            }
            //获取自定义的过滤方法
            $method = self::WHERE_METHOD[$key] ?? null;
            //判断是否有自定义的过滤方法
            if (!is_null($method) && !empty($value)) {
                $this->$method($query, $key, $value);
            } elseif (!empty($value) && is_array($value)) { //判断是否数组
                $query->whereIn("{$query->getModel()->getTable()}.{$key}", $value);
            } elseif (!empty($value)) { //值不为空
                $query->where("{$query->getModel()->getTable()}.{$key}", $value);
            }
        }
        return $query;
    }

    /**
     * 时间
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function dateWhere($query, $key, $value): void
    {
        $query->where("{$query->getModel()->getTable()}.stream_date", $key === 'start_date' ? '>=' : '<=', $value);
    }

    /**
     * 账号ID
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function accountIdWhere($query, $key, $value): void
    {
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $query->where(function ($builder) use ($table, $key, $value) {
            return $builder->whereIn("{$table}.{$key}", $value);
        });
    }

    /**
     * 服务器ID
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function serverIdWhere($query, $key, $value): void
    {
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $query->where(function ($builder) use ($table, $key, $value) {
            return $builder->whereIn("{$table}.{$key}", $value);
        });
    }

    /**
     * 角色ID
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function roleIdWhere($query, $key, $value): void
    {
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $query->where(function ($builder) use ($table, $key, $value) {
            return $builder->whereIn("{$table}.{$key}", $value);
        });
    }

    /**
     * 角色名称
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function roleNameWhere($query, $key, $value): void
    {
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $query->where(function ($builder) use ($table, $key, $value) {
            return $builder->whereRaw("{$table}.{$key} like ?", ["%{$value}%"]);
        });
    }

    /**
     * 执行动作
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function actionWhere($query, $key, $value): void
    {
        $subSql = StarRocksDB::toSql(DB::table(AntiChectHitLog::TABLE_NAME)
            ->select(['session_id'])
            ->whereIn($key, $value)
            ->groupBy('session_id'));
        $query->whereRaw("{$query->getModel()->getTable()}.session_id in ($subSql)");
    }

    /**
     * IP地址
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function ipAddressWhere($query, $key, $value): void
    {
        $query->whereIn("{$query->getModel()->getTable()}.ip", $value);
    }

    /**
     * 上报类型
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function reportTypeWhere($query, $key, $value): void
    {
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $query->where(function ($builder) use ($table, $key, $value) {
            // 判断 value 为0的
            if ($value == 0) {
                return $builder->where("{$table}.is_game_upload", 0)
                    ->orWhereNull("{$table}.is_game_upload");
            }
            return $builder->where("{$table}.is_game_upload", $value);
        });
    }

    /**
     * 风险等级
     *
     * @param $query
     * @param $key
     * @param $value
     * @return void
     */
    protected function riskLevelWhere($query, $key, $value): void
    {
        $query->where(function ($builder) use ($key, $value) {
            $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
            // 高风险
            $subSql = StarRocksDB::toSql(DB::table(AntiChectHitLog::TABLE_NAME)
                ->select(['session_id'])
                ->where('risk_level', SwitchConfig::HIGH_RISK)
                ->groupBy('session_id'));
            // 中风险
            $subSqlMiddle = StarRocksDB::toSql(DB::table($table)
                ->select(['session_id'])
                ->where('risk_level', SwitchConfig::MIDDLE_RISK)
                ->groupBy('session_id'));
            if (in_array(static::HIGH_RISK, $value)) {
                $builder->orWhereRaw("{$builder->getModel()->getTable()}.session_id in ($subSql)");
            }
            if (in_array(static::MIDDLE_RISK, $value)) {
                $builder->orWhere(function ($query) use ($subSql, $subSqlMiddle) {
                    return $query->whereRaw("{$query->getModel()->getTable()}.session_id not in ($subSql)")
                        ->whereRaw("{$query->getModel()->getTable()}.session_id in ($subSqlMiddle)");
                });
            }
            if (in_array(static::LOW_RISK, $value)) {
                $builder->orWhere(function ($query) use ($table, $subSql, $subSqlMiddle) {
                    return $query->whereRaw("{$query->getModel()->getTable()}.session_id not in ($subSql)")
                        ->whereRaw("{$query->getModel()->getTable()}.session_id not in ($subSqlMiddle)")
                        ->where(function ($query) use ($table) {
                            return $query->orWhereRaw("{$table}.write_info like '%crwxrwxrwx%'")
                                ->orWhereRaw("{$table}.write_info like '%crw-rwxrwx%'")
                                ->orWhereRaw("{$query->getModel()->getTable()}.resolution = ?", '720*1080')
                                ->orWhereRaw("{$query->getModel()->getTable()}.screen_density_dpi = ?", '320')
                                ->orWhereRaw("{$query->getModel()->getTable()}.is_emulator = ?", 1)
                                ->orWhereRaw("{$query->getModel()->getTable()}.is_root = ?", 1);
                        });
                });
            }
            return $builder;
        });
    }

    /**
     * 获取风险标签
     *
     * @param array $data
     * @return string
     */
    protected function getRiskTag($data)
    {
        $riskTag = [];
        // 判断是否有模拟器
        if ($data["is_emulator"]) {
            $riskTag[] = "环境风险-模拟器-{$data['device_brand']}";
        }
        // 判断write_info的内容中是否包含 crwxrwxrwx 或 crw-rwxrwx 字符串
        if (strpos($data["write_info"], "crwxrwxrwx") !== false || strpos($data["write_info"], "crw-rwxrwx") !== false) {
            $riskTag[] = "环境风险-文件权限-拥有写入权限";
        }
        // 判断是否有ROOT权限
        if ($data["is_root"]) {
            $riskTag[] = "环境风险-ROOT-通用Root";
        }
        // 判断是否有模拟点击_安装-F_移动小精灵
        if ($data['is_write']) {
            $riskTag[] = "外挂风险-模拟点击_安装-F_移动小精灵";
        }
        // 判断设备DPI是否为320
        if ($data["screen_density_dpi"] == 320) {
            $riskTag[] = "设备风险-DPI-320";
        }
        // 判断设备分辨率是否为720*1280
        if ($data["resolution"] == "720*1280") {
            $riskTag[] = "设备风险-分辨率-720*1280";
        }
        // 判断应用列表是否为空
        if (!empty($data["application_info"])) {
            $applicationInfo = json_decode($data["application_info"], true);
            foreach ($applicationInfo as $val) {
                // 判断应用是否在外挂风险应用列表中
                if (in_array($val["app_packageName"], OverviewService::CHECT_APP_LIST)) {
                    $riskTag[] = "外挂风险-模拟点击_安装-F_{$val["app_name"]}";
                }
            }
            // 判断应用列表长度，如果只有两个应用，则再需要判断一下特殊外挂应用
            if (count($applicationInfo) == 2) {
                foreach ($applicationInfo as $val) {
                    // 判断应用是否在外挂风险应用列表中
                    if (in_array($val["app_packageName"], OverviewService::CHECT_SPECIAL_APP_LIST)) {
                        $riskTag[] = "外挂风险-模拟点击_安装-F_{$val["app_name"]}";
                    }
                }
            }
        }
        // 判断是否有端口信息
        if (!empty($data["port_info"])) {
            $portInfo = json_decode($data["port_info"], true);
            foreach ($portInfo as $val) {
                $riskTag[] = "外挂风险-模拟点击_安装-F_{$val}";
            }
        }
        // 返回结果
        return implode("；", $riskTag);
    }
}
