<?php

/**
 * 修改开关表风险等级字段的数据库迁移文件
 * @desc 修改开关表风险等级字段的数据库迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/11
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlertRiskLevelToSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('external_plugin')->table('switch_config', function (Blueprint $table) {
            $table->string('risk_level')->default('[1]')
                ->comment('风险等级【1低风险2中风险3高风险4正常5可信源】')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('external_plugin')->table('switch_config', function (Blueprint $table) {
            $table->unsignedTinyInteger('risk_level')->default(1)
                ->comment('风险等级【1低风险2中风险3高风险4正常5可信源】')->change();
        });
    }
}
