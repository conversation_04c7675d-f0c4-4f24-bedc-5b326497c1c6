<?php

/**
 * 黑名单校验类
 * @desc 黑名单校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/09
 */

namespace App\Http\Validation;

use App\Models\MySQL\Blacklist;
use Illuminate\Validation\Rule;

/**
 * @method static BlacklistValidation build()
 */
class BlacklistValidation extends BaseValidation
{
    /**
     * 研发效能APP项目id校验
     * 
     * @return $this
     */
    public function developerAppId(): BlacklistValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 黑名单主键id
     * 
     * @return $this
     */
    public function blacklistId(): BlacklistValidation
    {
        $this->rules['blacklist_id'] = 'required';
        return $this;
    }

    /**
     * 黑名单有效期开始时间
     *
     * @return $this
     */
    public function startDate(): BlacklistValidation
    {
        $this->rules['start_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 黑名单有效期结束时间
     *
     * @return $this
     */
    public function endDate(): BlacklistValidation
    {
        $this->rules['end_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 账号ID
     *
     * @return $this
     */
    public function accountId(): BlacklistValidation
    {
        $this->rules['account_id'] = 'string';
        return $this;
    }

    /**
     * 设备ID
     *
     * @return $this
     */
    public function serverDevStr(): BlacklistValidation
    {
        $this->rules['server_dev_str'] = 'string';
        return $this;
    }

    /**
     * IP地址
     *
     * @return $this
     */
    public function ip(): BlacklistValidation
    {
        $this->rules['ip'] = 'string';
        return $this;
    }


    /**
     * 黑名单类型
     * @return BlacklistValidation
     */
    public function blacklistType(): BlacklistValidation
    {
        $this->rules['blacklist_type'] = ['required', Rule::in([blacklist::BLACKLIST_TYPE_SERVICE_DEV_STR, blacklist::BLACKLIST_TYPE_IP, blacklist::BLACKLIST_TYPE_ACCOUNT_ID])];
        return $this;
    }

    /**
     * 搜索栏
     * 
     * @return BlacklistValidation
     */
    public function search(): BlacklistValidation
    {
        $this->rules['search'] = 'string';
        return $this;
    }

    /**
     * 黑名单状态
     * 
     * @return BlacklistValidation
     */
    public function status(): BlacklistValidation
    {
        $this->rules['status'] = Rule::in([blacklist::BLACKLIST_STATUS_VALID, blacklist::BLACKLIST_STATUS_INVALID]);
        return $this;
    }

    /**
     * 添加原因
     * 
     * @return BlacklistValidation
     */
    public function addCause(): BlacklistValidation
    {
        $this->rules['add_cause'] = 'string';
        return $this;
    }

    /**
     * 排序字段
     *
     * @return $this
     */
    public function sortField(): BlacklistValidation
    {
        $this->rules['sort_field'] = 'string';
        return $this;
    }

    /**
     * 排序类型
     *
     * @return $this
     */
    public function sortType(): BlacklistValidation
    {
        $this->rules['sort_type'] = 'string';
        return $this;
    }

    /**
     * 分页
     *
     * @return $this
     */
    public function page(): BlacklistValidation
    {
        $this->rules['page'] = 'integer';
        return $this;
    }

    /**
     * 分页大小
     *
     * @return $this
     */
    public function prePage(): BlacklistValidation
    {
        $this->rules['pre_page'] = 'integer';
        return $this;
    }
}
