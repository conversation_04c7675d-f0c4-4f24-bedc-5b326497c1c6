<?php

/**
 * 开关配置校验类
 * @desc 开关配置校验类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/28
 */

namespace App\Http\Validation;

use App\Models\MySQL\SwitchConfig;

/**
 * @method static SwitchConfigValidation build()
 */
class SwitchConfigValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): SwitchConfigValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 平台
     *
     * @return $this
     */
    public function osType(): SwitchConfigValidation
    {
        $this->rules['os_type'] = 'integer';
        return $this;
    }

    /**
     * 是否开启【1开启0关闭】
     *
     * @return $this
     */
    public function isActive(): SwitchConfigValidation
    {
        $this->rules['is_active'] = 'integer';
        return $this;
    }

    /**
     * 错误上报间隔
     *
     * @return $this
     */
    public function errorReportInterval(): SwitchConfigValidation
    {
        $this->rules['error_report_interval'] = 'integer';
        return $this;
    }

    /**
     * 	风险等级【1低风险2中风险3高风险4正常5可信源】
     *
     * @return $this
     */
    public function riskLevel(): SwitchConfigValidation
    {
        $this->rules['risk_level'] = 'array';
        return $this;
    }

    /**
     * 执行动作【1闪退2弹窗提示99回调给项目组】
     *
     * @return $this
     */
    public function execAction(): SwitchConfigValidation
    {
        $this->rules['exec_action'] = 'integer';
        return $this;
    }

    /**
     * 弹窗内容
     * @return $this
     */
    public function popupContent(): SwitchConfigValidation
    {
        // 当exec_action=2时，popup_content参数必传
        $this->sometimeRules['popup_content'] = ['required', function ($input) {
            return $input->exec_action == SwitchConfig::EXEC_ACTION_POPUP;
        }];
        return $this;
    }

    /**
     * 闪退最低频率
     * @return $this
     */
    public function crashStartFrequency(): SwitchConfigValidation
    {
        // 当exec_action=1时，crash_start_frequency参数必传
        $this->sometimeRules['crash_start_frequency'] = ['required', function ($input) {
            // return $input->exec_action == SwitchConfig::EXEC_ACTION_CRASH;
            return in_array($input->exec_action, [SwitchConfig::EXEC_ACTION_CRASH, SwitchConfig::EXEC_ACTION_POPUP]);
        }];
        return $this;
    }

    /**
     * 闪退最高频率
     * @return $this
     */
    public function crashEndFrequency(): SwitchConfigValidation
    {
        // 当exec_action=1时，crash_end_frequency参数必传
        $this->sometimeRules['crash_end_frequency'] = ['required', function ($input) {
            // return $input->exec_action == SwitchConfig::EXEC_ACTION_CRASH;
            return in_array($input->exec_action, [SwitchConfig::EXEC_ACTION_CRASH, SwitchConfig::EXEC_ACTION_POPUP]);
        }];
        return $this;
    }
}
