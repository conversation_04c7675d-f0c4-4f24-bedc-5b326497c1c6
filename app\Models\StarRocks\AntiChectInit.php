<?php

/**
 * 对应starRocks数仓中anti_chect_init表
 * @desc 对应starRocks数仓中anti_chect_init表
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

use App\Services\Plugin\BaseService;

class AntiChectInit extends BaseModel
{
    public const TABLE_NAME = 'anti_chect_init';

    protected $table = self::TABLE_NAME;

    /**
     * 安卓
     *
     * @var int
     */
    const ANDROID = 1;

    /**
     * IOS
     *
     * @var int
     */
    const IOS = 2;

    /**
     * PC
     *
     * @var int
     */
    const PC = 3;

    /**
     * 平台
     *
     * @var array
     */
    const PLATFORM = [
        self::ANDROID => '安卓',
        self::IOS => '苹果',
        self::PC => 'PC',
    ];

    /**
     * 真机
     *
     * @var int
     */
    const REAL = 0;

    /**
     * 模拟器
     *
     * @var int
     */
    const SIMULATOR = 1;

    /**
     * 手机类型
     *
     * @var array
     */
    const PHONE_TYPE = [
        self::REAL => '真机',
        self::SIMULATOR => '模拟器',
    ];

    /**
     * 风险等级
     *
     * @var array
     */
    const RISK_LEVEL = [
        0 => '无风险',
        BaseService::LOW_RISK => '低风险',
        BaseService::MIDDLE_RISK => '中风险',
        BaseService::HIGH_RISK => '高风险',
    ];
}
