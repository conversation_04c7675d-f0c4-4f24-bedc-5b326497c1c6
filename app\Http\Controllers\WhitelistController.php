<?php

/**
 * 白名单控制器
 * @desc 白名单控制器
 * <AUTHOR> <EMAIL>
 * @date 2023/12/26
 */

namespace App\Http\Controllers;

use App\Http\Validation\WhitelistValidation;
use App\Models\MySQL\Whitelist;
use App\Services\Plugin\WhitelistService;
use Exception;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class WhitelistController extends Controller
{
    /**
     * 白名单列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12656
     * @throws HttpResponseException
     */
    public function list()
    {
        $params = WhitelistValidation::build()
            ->developerAppId()->whitelistType()
            ->search()->status()
            ->sortField()->sortType()
            ->page()->prePage()
            ->validate();
        try {
            $result = (new WhitelistService($params))->getWhitelist();
            return $this->success($result);
        } catch (Exception $e) {
            Log::error('白名单列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 白名单添加
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12658
     * @throws HttpResponseException
     */
    public function add()
    {
        $params = WhitelistValidation::build()
            ->developerAppId()->startDate()->endDate()->addCause()
            ->serverDevStr()->ip()->accountId()
            ->whitelistType()
            ->validate();
        try {
            list($isSuccess, $message) = (new WhitelistService($params))->addWhitelist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('白名单添加接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 白名单编辑
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12659
     * @throws HttpResponseException
     */
    public function edit()
    {
        $params = WhitelistValidation::build()
            ->developerAppId()
            ->whitelistId()
            ->startDate()->endDate()
            ->addCause()->validate();
        try {
            list($isSuccess, $message) = (new WhitelistService($params))->editWhitelist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('白名单编辑接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 白名单删除
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12660
     * @throws HttpResponseException
     */
    public function delete()
    {
        $params = WhitelistValidation::build()
            ->developerAppId()
            ->whitelistId()
            ->validate();
        try {
            list($isSuccess, $message) = (new WhitelistService($params))->deleteWhitelist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('白名单删除接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
