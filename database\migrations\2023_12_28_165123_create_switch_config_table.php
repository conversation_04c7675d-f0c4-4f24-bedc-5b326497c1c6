<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('external_plugin')->create('switch_config', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(1)->comment('系统类型【1android 2ios】');
            $table->unsignedTinyInteger('is_active')->default(1)->comment('是否开启【1开启0关闭】');
            $table->unsignedTinyInteger('error_report_interval')->default(30)->comment('错误上报间隔');
            $table->unsignedTinyInteger('risk_level')->default(1)->comment('风险等级【1低风险2中风险3高风险4正常5可信源】');
            $table->unsignedTinyInteger('exec_action')->default(1)->comment('执行动作【1闪退2弹窗提示3白名单跳过99回调给项目组】');
            $table->unsignedInteger('crash_start_frequency')->default(0)->comment('闪退最低次数');
            $table->unsignedInteger('crash_end_frequency')->default(0)->comment('闪退最高次数');
            $table->string('popup_content', 255)->default('')->comment('弹出内容，exec_action=2时必传');
            $table->timestamps();
            $table->unique(["developer_app_id", "os_type"]);
        });
        \DB::connection('external_plugin')->statement("ALTER TABLE `switch_config` comment '开关配置'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('external_plugin')->dropIfExists('switch_config');
    }
}
