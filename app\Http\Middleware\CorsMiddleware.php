<?php

/**
 * 跨域中间件
 * @desc 跨域中间件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CorsMiddleware
{
    /**
     * 跨域中间建
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $origin = $request->server('HTTP_ORIGIN') ? $request->server('HTTP_ORIGIN') : '';
        header("Access-Control-Allow-Origin:" . $origin);
        header("Access-Control-Allow-Credentials:true");
        header("Access-Control-Allow-Methods:POST,GET,OPTIONS,PUT,DELETE");
        header("Access-Control-Allow-Headers:Content-Type,Access-Token");
        header("Access-Control-Expose-Headers:*");

        return $next($request);
    }
}
