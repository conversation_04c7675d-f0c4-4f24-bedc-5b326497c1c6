<?php
/**
 * 白名单校验类
 * @desc 白名单校验类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/26
 */

namespace App\Http\Validation;

use App\Models\MySQL\Whitelist;
use Illuminate\Validation\Rule;

/**
 * @method static WhitelistValidation build()
 */
class WhitelistValidation extends BaseValidation
{
    /**
     * 研发效能APP项目id校验
     * @return $this
     */
    public function developerAppId(): WhitelistValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 白名单主键id
     * @return $this
     */
    public function whitelistId(): WhitelistValidation
    {
        $this->rules['whitelist_id'] = 'required';
        return $this;
    }

    /**
     * 白名单有效期开始时间
     *
     * @return $this
     */
    public function startDate(): WhitelistValidation
    {
        $this->rules['start_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 白名单有效期结束时间
     *
     * @return $this
     */
    public function endDate(): WhitelistValidation
    {
        $this->rules['end_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 账号ID
     *
     * @return $this
     */
    public function accountId(): WhitelistValidation
    {
        $this->rules['account_id'] = 'string';
        return $this;
    }

    /**
     * 设备ID
     *
     * @return $this
     */
    public function serverDevStr(): WhitelistValidation
    {
        $this->rules['server_dev_str'] = 'string';
        return $this;
    }

    /**
     * IP地址
     *
     * @return $this
     */
    public function ip(): WhitelistValidation
    {
        $this->rules['ip'] = 'string';
        return $this;
    }


    /**
     * 白名单类型
     * @return WhitelistValidation
     */
    public function whitelistType(): WhitelistValidation
    {
        $this->rules['whitelist_type'] = ['required', Rule::in([Whitelist::WHITELIST_TYPE_SERVICE_DEV_STR, Whitelist::WHITELIST_TYPE_IP, Whitelist::WHITELIST_TYPE_ACCOUNT_ID])];
        return $this;
    }

    /**
     * 搜索栏
     * @return WhitelistValidation
     */
    public function search(): WhitelistValidation
    {
        $this->rules['search'] = 'string';
        return $this;
    }

    /**
     * 白名单状态
     * @return WhitelistValidation
     */
    public function status(): WhitelistValidation
    {
        $this->rules['status'] = Rule::in([Whitelist::WHITELIST_STATUS_VALID, Whitelist::WHITELIST_STATUS_INVALID]);
        return $this;
    }

    /**
     * 添加原因
     * @return WhitelistValidation
     */
    public function addCause(): WhitelistValidation
    {
        $this->rules['add_cause'] = 'string';
        return $this;
    }

    /**
     * 排序字段
     *
     * @return $this
     */
    public function sortField(): WhitelistValidation
    {
        $this->rules['sort_field'] = 'string';
        return $this;
    }

    /**
     * 排序类型
     *
     * @return $this
     */
    public function sortType(): WhitelistValidation
    {
        $this->rules['sort_type'] = 'string';
        return $this;
    }

    /**
     * 分页
     *
     * @return $this
     */
    public function page(): WhitelistValidation
    {
        $this->rules['page'] = 'integer';
        return $this;
    }

    /**
     * 分页大小
     *
     * @return $this
     */
    public function prePage(): WhitelistValidation
    {
        $this->rules['pre_page'] = 'integer';
        return $this;
    }
}
