{"info": {"_postman_id": "global-config-api-tests", "name": "全局配置API测试", "description": "全局配置功能的API接口测试用例集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取全局配置信息", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "pm.test(\"Response data contains developer_app_id\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('developer_app_id');", "    pm.expect(jsonData.data).to.have.property('config_data');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/global-config/getConfigInfo?developer_app_id={{developer_app_id}}", "host": ["{{base_url}}"], "path": ["global-config", "getConfigInfo"], "query": [{"key": "developer_app_id", "value": "{{developer_app_id}}"}]}}, "response": []}, {"name": "编辑全局配置 - 基础配置", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates success\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.message).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"developer_app_id\": {{developer_app_id}},\n    \"config_data\": {\n        \"url\": \"https://www.baidu.com\",\n        \"status\": 1,\n        \"timeout\": 30\n    }\n}"}, "url": {"raw": "{{base_url}}/global-config/edit", "host": ["{{base_url}}"], "path": ["global-config", "edit"]}}, "response": []}, {"name": "编辑全局配置 - 复杂配置", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates success\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.message).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"developer_app_id\": {{developer_app_id}},\n    \"config_data\": {\n        \"api_settings\": {\n            \"base_url\": \"https://api.example.com\",\n            \"timeout\": 30,\n            \"retry_count\": 3\n        },\n        \"feature_flags\": {\n            \"enable_cache\": true,\n            \"enable_logging\": false,\n            \"max_connections\": 100\n        },\n        \"business_config\": {\n            \"payment_methods\": [\"alipay\", \"wechat\", \"bank\"],\n            \"default_currency\": \"CNY\",\n            \"tax_rate\": 0.06\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/global-config/edit", "host": ["{{base_url}}"], "path": ["global-config", "edit"]}}, "response": []}, {"name": "参数验证测试 - 缺少developer_app_id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 (<PERSON><PERSON> validation error)\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates validation error\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(422);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"config_data\": {\n        \"url\": \"https://www.baidu.com\",\n        \"status\": 1\n    }\n}"}, "url": {"raw": "{{base_url}}/global-config/edit", "host": ["{{base_url}}"], "path": ["global-config", "edit"]}}, "response": []}, {"name": "参数验证测试 - 缺少config_data", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 (<PERSON><PERSON> validation error)\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates validation error\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(422);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"developer_app_id\": {{developer_app_id}}\n}"}, "url": {"raw": "{{base_url}}/global-config/edit", "host": ["{{base_url}}"], "path": ["global-config", "edit"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost", "type": "string"}, {"key": "developer_app_id", "value": "1", "type": "string"}]}