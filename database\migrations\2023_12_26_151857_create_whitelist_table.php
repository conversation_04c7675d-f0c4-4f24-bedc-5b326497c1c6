<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhitelistTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('external_plugin')->create('whitelist', function (Blueprint $table) {
            $table->bigIncrements('whitelist_id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('server_dev_str', 256)->default('')->comment('设备ID');
            $table->string('ip', 64)->default('')->comment('IP');
            $table->string('account_id', 64)->default('')->comment('账号ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('白名单状态，0：无效，1：有效');
            $table->unsignedTinyInteger('whitelist_type')->default(0)->comment('白名单类型，1：设备，2：IP，3：账号');
            $table->dateTime('start_date')->nullable()->comment('白名单有效开始时间');
            $table->dateTime('end_date')->nullable()->comment('白名单有效结束时间（白名单到期时间）');
            $table->text('add_cause')->nullable()->comment('添加原因');
            $table->softDeletes(); // 软删除字段
            $table->timestamps();
            $table->index('whitelist_type');
            $table->index('server_dev_str');
            $table->index('ip');
            $table->index('account_id');
            $table->index('end_date');
        });
        \DB::connection('external_plugin')->statement("ALTER TABLE `whitelist` comment '白名单管理表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('external_plugin')->dropIfExists('whitelist');
    }
}
