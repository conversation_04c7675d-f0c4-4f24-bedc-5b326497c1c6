<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Sanctum\Sanctum;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
        Sanctum::ignoreMigrations(); // 默认会生成 Sanctum 的预设表 personal_access_tokens，配置Sanctum::ignoreMigrations()忽略创建
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
