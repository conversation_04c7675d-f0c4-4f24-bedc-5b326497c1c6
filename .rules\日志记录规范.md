# 日志记录规范

## 概述

本文档定义了项目中日志记录的标准格式和最佳实践，确保日志的一致性、可读性和可维护性。

## 核心原则

### 1. 字符串连接格式
- **强制要求**：所有Log语句（Log::info、Log::error、Log::warning等）必须使用字符串连接格式
- **禁止使用**：数组格式的日志记录
- **目的**：保持日志输出的简洁性和一致性

### 2. 一行显示原则
- 所有日志信息应在一行内完整显示
- 便于日志查看、搜索和分析
- 提高日志可读性

## 格式规范

### Log::error 格式

**标准格式**：
```php
Log::error('类名::方法名 操作描述失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine() . ' 附加信息');
```

**必须包含的信息**：
1. **类名::方法名** - 明确错误发生的位置
2. **操作描述** - 简要说明正在执行的操作
3. **错误原因** - `$e->getMessage()`
4. **文件位置** - `$e->getFile()`
5. **行号** - `$e->getLine()`
6. **附加信息** - 如参数、上下文等（可选）

**示例**：
```php
// 控制器中的错误日志
Log::error('BarChartController::getData 获取柱状图数据失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine() . ' params: ' . json_encode($params));

// 服务类中的错误日志
Log::error('BarChartService::getStatisticsData 获取统计数据失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
```

### Log::info 格式

**标准格式**：
```php
Log::info('类名::方法名 操作描述' . ' 附加信息');
```

**示例**：
```php
Log::info('BarChartController::getData 开始获取柱状图数据' . ' params: ' . json_encode($params));
Log::info('BarChartService::getData 数据处理完成');
```

### Log::warning 格式

**标准格式**：
```php
Log::warning('类名::方法名 警告描述' . ' 详细信息');
```

## 禁止的格式

### ❌ 错误示例 - 数组格式
```php
// 禁止使用数组格式
Log::error('操作失败', [
    'error' => $e->getMessage(),
    'file' => $e->getFile(),
    'line' => $e->getLine(),
    'params' => $params
]);

Log::info('操作成功', [
    'data_count' => count($data),
    'params' => $params
]);
```

### ❌ 错误示例 - 多行格式
```php
// 禁止多行格式
Log::error('操作失败：' . $e->getMessage() . 
    ' 文件：' . $e->getFile() . 
    ' 行号：' . $e->getLine());
```

## 特殊情况处理

### 1. 复杂参数记录
当需要记录复杂参数时，使用`json_encode()`转换：
```php
Log::error('UserService::createUser 创建用户失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine() . ' params: ' . json_encode($params));
```

### 2. 敏感信息处理
记录日志时注意过滤敏感信息：
```php
// 过滤密码等敏感信息
$safeParams = array_diff_key($params, ['password' => '', 'token' => '']);
Log::error('AuthService::login 登录失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine() . ' params: ' . json_encode($safeParams));
```

### 3. 长字符串处理
对于过长的字符串，适当截断：
```php
$shortMessage = mb_strlen($message) > 200 ? mb_substr($message, 0, 200) . '...' : $message;
Log::info('MessageService::send 发送消息' . ' content: ' . $shortMessage);
```

## 命名约定

### 1. 类名和方法名
- 使用完整的类名（不包含命名空间）
- 使用实际的方法名
- 格式：`ClassName::methodName`

### 2. 操作描述
- 使用中文描述
- 简洁明了，动词+名词
- 例如：获取数据、创建用户、发送邮件

### 3. 状态描述
- 成功：操作描述（如：数据获取成功）
- 失败：操作描述失败（如：数据获取失败）
- 开始：开始+操作描述（如：开始获取数据）

## 性能考虑

### 1. 避免复杂计算
```php
// ✅ 正确 - 简单字符串连接
Log::info('UserService::getUsers 获取用户列表成功' . ' count: ' . count($users));

// ❌ 错误 - 复杂计算
Log::info('UserService::getUsers 获取用户列表成功' . ' users: ' . json_encode($users)); // 大数组会影响性能
```

### 2. 条件日志记录
对于调试日志，考虑使用条件记录：
```php
if (config('app.debug')) {
    Log::info('DebugService::process 调试信息' . ' data: ' . json_encode($debugData));
}
```

## 检查清单

在提交代码前，请确认：

- [ ] 所有Log语句使用字符串连接格式
- [ ] 错误日志包含完整的错误信息（原因、文件、行号）
- [ ] 日志描述清晰明了
- [ ] 敏感信息已过滤
- [ ] 日志格式符合项目规范

## 工具支持

### IDE配置
建议在IDE中配置代码模板：

**Log::error模板**：
```php
Log::error('$CLASS$::$METHOD$ $DESCRIPTION$失败，原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine()$PARAMS$);
```

**Log::info模板**：
```php
Log::info('$CLASS$::$METHOD$ $DESCRIPTION$'$PARAMS$);
```

## 版本历史

- **v1.0** (2025-06-12) - 初始版本，定义基础日志格式规范
- **v1.1** (2025-06-12) - 添加特殊情况处理和性能考虑
