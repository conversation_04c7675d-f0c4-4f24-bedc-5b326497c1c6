<?php

/**
 * 黑名单控制器
 * @desc 黑名单控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/01/09
 */

namespace App\Http\Controllers;

use App\Http\Validation\BlacklistValidation;
use App\Services\Plugin\BlacklistService;
use Exception;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class BlacklistController extends Controller
{
    /**
     * 黑名单列表
     * 
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12711
     * @throws HttpResponseException
     */
    public function list()
    {
        $params = BlacklistValidation::build()
            ->developerAppId()->blacklistType()->search()->status()
            ->sortField()->sortType()->page()->prePage()
            ->validate();
        try {
            $result = (new BlacklistService($params))->getBlacklist();
            return $this->success($result);
        } catch (Exception $e) {
            Log::error('黑名单列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 黑名单添加
     * 
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12712
     * @throws HttpResponseException
     */
    public function add()
    {
        $params = BlacklistValidation::build()
            ->developerAppId()->startDate()->endDate()->addCause()
            ->serverDevStr()->ip()->accountId()->blacklistType()
            ->validate();
        try {
            list($isSuccess, $message) = (new BlacklistService($params))->addBlacklist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('黑名单添加接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 黑名单编辑
     * 
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12713
     * @throws HttpResponseException
     */
    public function edit()
    {
        $params = BlacklistValidation::build()
            ->developerAppId()->blacklistId()->startDate()->endDate()->addCause()
            ->validate();
        try {
            list($isSuccess, $message) = (new BlacklistService($params))->editBlacklist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('黑名单编辑接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 黑名单删除
     * 
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12714
     * @throws HttpResponseException
     */
    public function delete()
    {
        $params = BlacklistValidation::build()
            ->developerAppId()->blacklistId()
            ->validate();
        try {
            list($isSuccess, $message) = (new BlacklistService($params))->deleteBlacklist();
            if (!$isSuccess) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, $message);
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('黑名单删除接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
