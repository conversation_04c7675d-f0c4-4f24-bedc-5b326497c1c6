<?php

/**
 * 验证签名
 * @desc 验证签名
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 202/02/23
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Middleware;

use App\Services\Http\ApiSign;
use Closure;
use Illuminate\Http\Request;

class VerifySignMiddleware
{
    /**
     * 验证签名
     *
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 验证签名是否正确
        // 判断是否有ts和sign参数
        if (!$request->has('ts') || !$request->has('sign')) {
            return response()->json(['code' => 1000, 'message' => '签名参数错误', 'data' => []]);
        }
        // 获取所有参数
        $params = $request->all();
        // 判断ts是否过期，超过2分钟则认为签名无效
        $ts = $params['ts'];
        if (!is_numeric($ts) || (time() - $ts) > 120) {
            return response()->json(['code' => 1001, 'message' => '签名已过期', 'data' => []]);
        }
        // 验证签名是否正确
        $sign = $params['sign'];
        if ($sign != ApiSign::sign($params, config('services.api_secret'))) {
            return response()->json(['code' => 1002, 'message' => '签名验证失败', 'data' => []]);
        }
        // 签名成功，继续执行请求
        return $next($request);
    }
}
