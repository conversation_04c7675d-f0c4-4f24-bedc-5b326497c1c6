<?php

/**
 * 黑名单管理模型类
 * @desc 黑名单管理模型类
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/09
 */

namespace App\Models\MySQL;

use Illuminate\Database\Eloquent\SoftDeletes;

class Blacklist extends BaseModel
{
    /**
     * 使用软删除
     */
    use SoftDeletes;

    /**
     * 黑名单类型
     */
    const BLACKLIST_TYPE_SERVICE_DEV_STR = 1; // 设备黑名单
    const BLACKLIST_TYPE_IP = 2; // IP黑名单
    const BLACKLIST_TYPE_ACCOUNT_ID = 3; // 账号黑名单

    /**
     *黑名单类型字段映射
     */
    const BLACKLIST_TYPE_FIELD_MAP = [
        self::BLACKLIST_TYPE_SERVICE_DEV_STR => 'server_dev_str',
        self::BLACKLIST_TYPE_IP => 'ip',
        self::BLACKLIST_TYPE_ACCOUNT_ID => 'account_id'
    ];

    /**
     * 黑名单状态
     */
    const BLACKLIST_STATUS_INVALID = 0; // 无效
    const BLACKLIST_STATUS_VALID = 1; // 有效

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'blacklist';

    /**
     * 主键字段
     *
     * @var string
     */
    protected $primaryKey = 'blacklist_id';

    /**
     * 允许写入的字段
     *
     * @var array
     */
    protected $fillable = [
        'developer_app_id', // 研发效能APP项目id
        'server_dev_str', // 设备ID
        'ip', // IP
        'account_id', // 账号ID
        'status', // 黑名单名单状态，0：无效，1：有效，2：永久有效
        'blacklist_type', // 黑名单类型，1：设备，2：IP，3：账号
        'start_date', // 黑名单有效开始时间
        'end_date', // 黑名单有效结束时间（黑名单到期时间）
        'add_cause', // 添加原因
    ];

    // 设置日期字段的格式
    public $timestamps = false;
}
