<?php

/**
 * 监控命中数据分布情况脚本
 * @desc 监控数据分布情况脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Models\MySQL\AppModel;
use App\Models\StarRocks\AntiChectHitLog;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MonitorHitDataCommand extends Command
{
    /**
     * 机器人webhook地址
     *
     * @var string
     */
    const HOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cffbb57a-3a02-407b-b320-fdaecafd20a2';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:hit:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控命中数据分布情况脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //打印日志
            Log::info("执行监控命中数据分布情况开始");
            // 获取昨天时间
            $startDate = Carbon::yesterday()->startOfDay()->toDateTimeString();
            $endDate = Carbon::yesterday()->endOfDay()->toDateTimeString();
            $yesterday = Carbon::yesterday()->toDateString();
            // 组装msg信息
            $content = "外挂昨日命中数据推送【{$yesterday}】：\n";
            // 获取数据
            $list = AntiChectHitLog::query()
                ->selectRaw('extra_app_id, count(*) as num, count(distinct server_dev_str) as dev_num')
                ->where('stream_date', '>=', $startDate)
                ->where('stream_date', '<=', $endDate)
                ->groupBy('extra_app_id')
                ->orderBy('num', 'desc')
                ->getFromSR();
            // 判断数据是否为空
            if (empty($list)) {
                return;
            }
            // 获取APP信息
            $apps = AppModel::query()->pluck('app_name', 'id');
            // 循环处理数据
            foreach ($list as $item) {
                $appName = $apps[$item['extra_app_id']] ?? '未知';
                $content .= "{$appName}：{$item['num']} 条，涉及 {$item['dev_num']} 设备\n";
            }
            // 把最后的换行符去掉
            $content = rtrim($content, "\n");
            // 发送请求
            $response = Http::asJson()->post(static::HOOK_URL, [
                'msgtype' => 'text',
                'text' => [
                    'content' => $content,
                ],
            ]);
            //打印日志
            Log::info("执行监控命中数据分布情况完成：{$response->body()}");
        } catch (\Exception $e) {
            Log::error("执行监控命中数据分布情况脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
