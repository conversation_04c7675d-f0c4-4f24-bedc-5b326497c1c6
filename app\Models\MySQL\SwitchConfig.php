<?php

/**
 * 开关配置模型类
 * @desc 开关配置模型类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/28
 */

namespace App\Models\MySQL;

class SwitchConfig extends BaseModel
{
    // 系统类型映射数组
    const OS_TYPE_MAP = [
        "1" => "android",
        "2" => "ios",
        "3" => "pc",
    ];

    // 执行动作-闪退
    const EXEC_ACTION_CRASH = 1;
    // 执行动作-弹窗提示
    const EXEC_ACTION_POPUP = 2;
    // 执行动作-白名单跳过
    const EXEC_ACTION_IP_WHITELIST = 3;
    // 执行动作-黑名单拦截
    const EXEC_ACTION_IP_BLACKLIST = 4;
    // 执行动作-回调给项目组
    const EXEC_ACTION_CALLBACK_PROJECT_TEAM = 99;

    /**
     * 高风险
     *
     * @var int
     */
    const HIGH_RISK = 3;

    /**
     * 中风险
     *
     * @var int
     */
    const MIDDLE_RISK = 2;

    /**
     * 低风险
     *
     * @var int
     */
    const LOW_RISK = 1;

    // pc默认配置填充值
    const FILL_PC_DEFAULT_CONFIG = [
        "os_type" => 3,
        "is_active" => 0,
        "error_report_interval" => 60,
        "risk_level" => [3],
        "exec_action" => 99,
        "popup_content" => "",
        "crash_start_frequency" => 1,
        "crash_end_frequency" => 10,
    ];

    // ios默认配置填充值
    const FILL_IOS_DEFAULT_CONFIG = [
        "os_type" => 2,
        "is_active" => 0,
        "error_report_interval" => 60,
        "risk_level" => [3],
        "exec_action" => 99,
        "popup_content" => "",
        "crash_start_frequency" => 1,
        "crash_end_frequency" => 10,
    ];

    // 安卓默认配置填充值
    const FILL_ANDROID_DEFAULT_CONFIG = [
        "os_type" => 1,
        "is_active" => 0,
        "error_report_interval" => 60,
        "risk_level" => [3],
        "exec_action" => 99,
        "popup_content" => "",
        "crash_start_frequency" => 1,
        "crash_end_frequency" => 10,
    ];

    protected $table = 'switch_config';
    protected $primaryKey = 'id';
    protected $fillable = [
        'developer_app_id', // 游戏id
        'os_type', // 系统类型【1android 2ios】
        'is_active', // 是否开启【1开启0关闭】
        'error_report_interval', // 错误上报间隔
        'risk_level', // 风险等级【1低风险2中风险3高风险4正常5可信源】
        'exec_action', // 执行动作【1闪退2弹窗提示99回调给项目组】
        'popup_content', // 弹出内容，exec_action=2时必传
        'crash_start_frequency', // 闪退最低频率，exec_action=1时必传
        'crash_end_frequency', // 闪退最高频率，exec_action=1时必传
    ];
}
