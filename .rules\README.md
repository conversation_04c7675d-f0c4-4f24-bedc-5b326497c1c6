# 项目开发规范

## 概述

本目录包含项目开发过程中必须遵循的各项规范和标准，确保代码质量、一致性和可维护性。

## 规范文件列表

### 1. [日志记录规范](./日志记录规范.md)
- **适用范围**：所有PHP代码中的日志记录
- **核心要求**：使用字符串连接格式，禁止数组格式
- **强制执行**：是

## 规范执行

### 强制性规范
以下规范为**强制执行**，所有开发人员必须严格遵守：

1. **日志记录规范** - 所有Log语句必须使用字符串连接格式

### 建议性规范
以下规范为**建议遵循**，有助于提高代码质量：

- 待补充

## 代码审查检查点

在代码审查时，请重点检查以下项目：

### 日志记录
- [ ] 所有Log::error包含完整错误信息（原因、文件、行号）
- [ ] 所有Log语句使用字符串连接格式
- [ ] 日志描述清晰明了，包含类名和方法名
- [ ] 敏感信息已过滤

### 代码质量
- [ ] 方法职责单一
- [ ] 常量定义清晰
- [ ] 异常处理完整
- [ ] 中文注释详细

## 规范更新

### 更新流程
1. 提出规范修改建议
2. 团队讨论和评审
3. 更新规范文档
4. 通知所有开发人员
5. 在新代码中执行新规范

### 版本管理
- 每次规范更新都应记录版本号和更新日期
- 重大变更应提前通知团队

## 工具支持

### IDE配置
- 配置代码模板以符合项目规范
- 设置代码检查规则
- 使用格式化工具保持代码风格一致

### 自动化检查
- 在CI/CD流程中集成代码规范检查
- 使用静态分析工具检测规范违规

## 联系方式

如有规范相关问题，请联系：
- 技术负责人：陈建权 <EMAIL>

## 更新历史

- **v1.0** (2025-06-12) - 初始版本，添加日志记录规范
