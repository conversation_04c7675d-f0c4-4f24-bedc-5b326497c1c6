<?php

/**
 * 概览页数据库查询server
 * @desc 概览页数据库查询server
 * <AUTHOR> <EMAIL>
 * @date 2023/12/27
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;
use App\Models\StarRocks\IpParse;
use App\Models\StarRocks\StarRocksDB;
use App\Services\IpServer;
use Illuminate\Support\Facades\DB;

class OverviewService extends BaseService
{
    /**
     * 外挂应用列表
     *
     * @var array
     */
    const CHECT_APP_LIST = [
        'com.angel.nrzs',
        'com.ccc.changan',
        'com.ypeng.changan',
        'com.wkcahx',
        'mhca888.apk',
        'com.nx.nxpro',
        'com.xxxhhgbt',
        'com.nx.nxproj',
        'com.baidu.dcsdf',
        'com.nx.hello',
        'com.xbsfz.dzcady',
        'com.com.xxxhhgbt',
        'com.seek.clicker',
        'yijianwan.apk',
        'com.cyjh.mobileanjian',
        'com.script.keypack',
        'com.changanhuanxiang',
        'com.cahx.yuduoduo.com',
        'com.mygolbs.mybus',
    ];

    /**
     * 特殊外挂应用列表
     *
     * @var array
     */
    const CHECT_SPECIAL_APP_LIST = [
        'com.www.baidu.com',
        'com.eg.android.AlipayGphone',
    ];

    /**
     * 获取联网设备数
     * @return int
     */
    public function getConnectedDevNum()
    {
        // SELECT COUNT( DISTINCT server_dev_str ) AS connectedDevNum FROM `anti_chect_init` WHERE ( `anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` = 1)
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as connected_dev_num"))->getFromSR();
        if (!empty($ret) && isset($ret[0]["connected_dev_num"])) {
            return intval($ret[0]["connected_dev_num"]);
        }

        return 0;
    }

    /**
     * 获取启动外挂设备数
     * @return int
     */
    public function getUseChectDevNum()
    {
        // SELECT COUNT( DISTINCT server_dev_str ) AS useChectDevNum FROM `anti_chect_init` INNER JOIN `anti_chect_hit_log` ON `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id` WHERE ( `anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` = 1)
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as use_chect_dev_num"))->getFromSR();
        if (!empty($ret) && isset($ret[0]["use_chect_dev_num"])) {
            return intval($ret[0]["use_chect_dev_num"]);
        }
        return 0;
    }

    /**
     * 启动用户数
     * @return int
     */
    public function getUseUserNum()
    {
        // SELECT COUNT( DISTINCT get_json_string ( game_info, "account_id" )) AS use_user_num FROM `anti_chect_init` INNER JOIN `anti_chect_upload_data_primary` ON `anti_chect_init`.`session_id` = `anti_chect_upload_data_primary`.`session_id` WHERE ( `anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` =1)
        $builder = $this->getInitJoinPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$table}.account_id) as use_user_num"))->firstFromSR();
        return intval($ret["use_user_num"] ?? 0);
    }

    /**
     * 启动外挂用户数
     * @return int
     */
    public function getUseChectUserNum()
    {
        // SELECT COUNT( DISTINCT get_json_string ( game_info, "account_id" )) AS useChectUserNum FROM `anti_chect_init` INNER JOIN `anti_chect_hit_log` ON `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id` INNER JOIN `anti_chect_upload_data_primary` ON `anti_chect_init`.`session_id` = `anti_chect_upload_data_primary`.`session_id` WHERE ( `anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` =1)
        $builder = $this->getInitJoinHitAndPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$table}.account_id) as use_chect_user_num"))->getFromSR();
        if (!empty($ret) && isset($ret[0]["use_chect_user_num"])) {
            return intval($ret[0]["use_chect_user_num"]);
        }
        return 0;
    }

    /**
     * 获取联网设备数趋势图数据
     * @return array
     */
    public function getConnectedDevNumTrend()
    {
        $mainTable = AntiChectInit::TABLE_NAME;
        $groupByField = $this->getDateFieldGroupByFormat();
        $builder = $this->getInitBuilder();
        $ret = $builder
            ->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as connected_dev_num, {$groupByField} as date"))
            ->groupByRaw($groupByField)->getFromSR();

        if (!empty($ret)) {
            return $ret;
        }

        return [];
    }

    /**
     * 启动外挂设备数趋势图数据
     * @return array
     */
    public function getUseChectDevNumTrend()
    {
        // SELECT COUNT( DISTINCT server_dev_str ) AS useChectDevNum, DATE_FORMAT( `anti_chect_init`.`stream_date`, '%Y-%m-%d' ) AS date FROM `anti_chect_init` INNER JOIN `anti_chect_hit_log` ON `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id`
        //WHERE ( `anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` = 1) GROUP BY `date`
        $mainTable = AntiChectInit::TABLE_NAME;
        $groupByField = $this->getDateFieldGroupByFormat();
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as use_chect_dev_num, {$groupByField} as date"))
            ->groupBy(["date"])
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }

        return [];
    }

    /**
     * 启动用户数趋势图数据
     * @return array
     */
    public function getUseUserNumTrend()
    {
        // SELECT COUNT( DISTINCT get_json_string ( game_info, 'account_id' )) AS use_user_num, DATE_FORMAT( `anti_chect_init`.`stream_date`, '%Y-%m-%d' ) AS date FROM `anti_chect_init` INNER JOIN `anti_chect_upload_data_primary` ON `anti_chect_init`.`session_id` = `anti_chect_upload_data_primary`.`session_id` WHERE(`anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` = 1) group by `date`
        $groupByField = $this->getDateFieldGroupByFormat();
        $builder = $this->getInitJoinPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$table}.account_id) as use_user_num, {$groupByField} as date"))
            ->groupBy(["date"])
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }
        return [];
    }

    /**
     * 启动外挂用户数趋势图数据
     * @return array
     */
    public function getUseChectUserNumTrend()
    {
        // SELECT COUNT( DISTINCT get_json_string ( game_info, 'account_id' )) AS useChectUserNum, DATE_FORMAT( `anti_chect_init`.`stream_date`, '%Y-%m-%d' ) AS date FROM `anti_chect_init` INNER JOIN `anti_chect_hit_log` ON `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id` INNER JOIN `anti_chect_upload_data_primary` ON `anti_chect_init`.`session_id` = `anti_chect_upload_data_primary`.`session_id` WHERE(`anti_chect_init`.`extra_app_id` = 1 AND `anti_chect_init`.`stream_date` >= "2023-12-21 16:43:07" AND `anti_chect_init`.`stream_date` <= "2023-12-27 16:43:07" AND `anti_chect_init`.`os_type` = 1) group by `date`
        $groupByField = $this->getDateFieldGroupByFormat();
        $builder = $this->getInitJoinHitAndPrimaryBuilder();
        $table = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$table}.account_id) as use_chect_user_num, {$groupByField} as date"))
            ->groupBy(["date"])
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }
        return [];
    }

    /**
     * 外挂设备品牌排行榜
     * @return array
     */
    public function getChectDevBrandRank()
    {
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as dev_num, device_brand as brand"))
            ->groupBy(["device_brand"])
            ->orderBy("dev_num", "desc")
            ->limit(10)
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }
        return [];
    }

    /**
     * 外挂设备IP排行榜
     * @return array
     */
    public function getChectDevIPRank()
    {
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as dev_num, {$mainTable}.ip as dev_ip"))
            ->groupBy(["dev_ip"])
            ->orderBy("dev_num", "desc")
            ->limit(10)
            ->getFromSR();
        if (!empty($ret)) {
            // 获取IP的地区信息列表
            $ipServer = IpServer::getInstance();
            // 格式化返回数据
            foreach ($ret as &$item) {
                // 获取ip地区信息
                $ipInfo = $ipServer->getResultAll($item['dev_ip']);
                // 如果没有地区信息，不显示
                if (empty($ipInfo)) {
                    continue;
                }
                // 修改ip地址显示
                $item['dev_ip'] = "{$item['dev_ip']} ({$ipInfo['country']}-{$ipInfo['province']}-{$ipInfo['city']})";
            }
            return $ret;
        }
        return [];
    }

    /**
     * 外挂应用列表排行
     * @return array
     */
    public function getChectAppRank()
    {
        // 获取筛选后的Builder
        $builder = $this->getInitJoinHitBuilder()->selectRaw(AntiChectInit::TABLE_NAME . '.session_id');
        // 获取子SQL
        $subSql = AntiChectUploadDataPrimary::query()
            ->selectRaw("server_dev_str, SPLIT(REGEXP_REPLACE(REGEXP_REPLACE(application_info, '\\\[|\\\]', ''), '},{', '}||{'), '||') AS application_info")
            ->whereRaw("session_id in ({$builder->getSQL()})")
            ->getSQL();
        // 特殊应用子SQL
        $specialSubSql = StarRocksDB::toSql(
            DB::table(Db::raw("({$subSql}) AS t"))
                ->whereRaw('array_length (application_info) = 2')
        );
        // 获取子SQL
        $subSql = StarRocksDB::toSql(
            DB::table(Db::raw("({$subSql}) AS t, UNNEST (`application_info`)"))
                ->selectRaw("server_dev_str, get_json_string (unnest, 'app_name') AS app_name, get_json_string (unnest, 'app_packageName') AS app_package")
        );
        // 特殊应用子SQL
        $specialSubSql = StarRocksDB::toSql(
            DB::table(Db::raw("({$specialSubSql}) AS t, UNNEST (`application_info`)"))
                ->selectRaw("server_dev_str, get_json_string (unnest, 'app_name') AS app_name, get_json_string (unnest, 'app_packageName') AS app_package")
        );
        // 查询列表数据
        $result = StarRocksDB::query(
            DB::table(Db::raw("({$subSql}) AS t"))
                ->selectRaw("app_name, app_package, COUNT(DISTINCT server_dev_str) AS dev_num")
                ->whereIn('app_package', self::CHECT_APP_LIST)
                ->groupBy('app_name', 'app_package')
                ->latest('dev_num')
                ->limit(10)
        )->get();
        // 查询特殊应用
        $specialResult = StarRocksDB::query(
            DB::table(Db::raw("({$specialSubSql}) AS t"))
                ->selectRaw("app_name, app_package, COUNT(DISTINCT server_dev_str) AS dev_num")
                ->whereIn('app_package', array_diff(self::CHECT_SPECIAL_APP_LIST, ['com.eg.android.AlipayGphone']))  // 排除支付宝的统计
                ->groupBy('app_name', 'app_package')
                ->latest('dev_num')
        )->get();
        // 合并结果
        $result = array_merge($result, $specialResult);
        // 对二维数组的 dev_num 字段按降序排序
        foreach ($result as &$item) {
            $item['dev_num'] = intval($item['dev_num']);
            $item['app_name'] = $item['app_name'] . " ({$item['app_package']})";
        }
        array_multisort(array_column($result, 'dev_num'), SORT_DESC, $result);
        // 返回结果，只要10条数据
        return array_slice($result, 0, 10);
    }

    /**
     * 外挂设备系统版本排行榜
     * @return array
     */
    public function getChectDevSystemRank()
    {
        // select COUNT(*) as useChectDevNum, app_name as appName from `anti_chect_init` inner join `anti_chect_hit_log` on `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id` where (`anti_chect_init`.`extra_app_id` = ? and `anti_chect_init`.`stream_date` >= ? and `anti_chect_init`.`stream_date` <= ? and `anti_chect_init`.`os_type`=?) group by `os_type`, `os_version` order by `useChectDevNum` desc
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as dev_num, os_type, os_version"))
            ->groupBy(["os_type", "os_version"])
            ->orderBy("dev_num", "desc")
            ->limit(10)
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }
        return [];
    }

    /**
     * 外挂设备执行动作排行榜
     *
     * @return array
     */
    public function getChectDevActionRank()
    {
        // select COUNT(*) as useChectDevNum as dev_num, action from `anti_chect_init` inner join `anti_chect_hit_log` on `anti_chect_init`.`session_id` = `anti_chect_hit_log`.`session_id` where (`anti_chect_init`.`extra_app_id` = ? and `anti_chect_init`.`stream_date` >= ? and `anti_chect_init`.`stream_date` <= ? and `anti_chect_init`.`os_type`=?) group by `action` order by dev_num desc
        $mainTable = AntiChectInit::TABLE_NAME;
        $builder = $this->getInitJoinHitBuilder();
        $ret = $builder->select($builder->raw("COUNT(DISTINCT {$mainTable}.server_dev_str) as dev_num, action"))
            ->groupBy(["action"])
            ->orderBy("dev_num", "desc")
            ->getFromSR();
        if (!empty($ret)) {
            return $ret;
        }
        return [];
    }

    /**
     * 根据查询时间返回group by字段的时间格式
     * @return string
     */
    protected function getDateFieldGroupByFormat()
    {
        $mainTable = AntiChectInit::TABLE_NAME;
        $params = $this->params;
        $groupByField = "DATE_FORMAT(`{$mainTable}`.`stream_date`, '%Y-%m-%d')";
        if (isset($params["start_date"]) && isset($params["end_date"])) {
            $mod = (strtotime($params["end_date"]) - strtotime($params["start_date"])) / (24 * 3600);
            // 时间跨度小于两天，按照小时groupBy
            if ($mod <= 2) {
                $groupByField = "DATE_FORMAT(`{$mainTable}`.`stream_date`, '%Y-%m-%d %H:00:00')";
            }
        }

        return $groupByField;
    }

    /**
     * 百分比计算格式化
     * @param $numerator
     * @param $denominator
     * @param $type 计算类型【1占比2环比】
     * @return float|int
     */
    public function percentFormat($numerator, $denominator, $type = 1)
    {
        if (empty($numerator)) {
            return 0;
        }

        if (empty($denominator)) {
            return 100;
        }

        // 权重值
        $weight = 0;
        if ($type == 2) {
            $weight = 1;
        }

        return round(($numerator / $denominator - $weight) * 100, 1);;
    }
}
