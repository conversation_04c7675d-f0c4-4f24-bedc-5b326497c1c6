<?php

/**
 * 同步AI数据的定时脚本
 * @desc 同步AI数据的定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/06/11
 */

namespace App\Console\Commands;

use App\Models\SDK\Login;
use App\Services\Plugin\ExternalDataProcessService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class SyncAiDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ai:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步AI数据的定时脚本';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("执行同步AI数据的定时脚本，开始时间：" . now()->toDateTimeString());

        $list = [];
        for ($i = 0; $i < 200; $i++) {
            $i++;
            // 从redis中获取数据
            $redisData = Redis::rPop('external_ai_data_queue');
            if (empty($redisData)) {
                break;
            }
            $item = json_decode($redisData, true);
            if (!empty($item)) {
                $list[] = $item;
            }
        }
        // 查询设备信息
        $devStrInfoList = [];
        if (!empty($list)) {
            $serverDevStrList = array_unique(array_column($list, 'server_dev_str'));
            // 查询设备id黑名单
            $devStrInfoList = Login::query()
                ->selectRaw('dev_str, ANY_VALUE(manufacturer) as manufacturer, ANY_VALUE(model) as device_model, ANY_VALUE(game_version_name) as app_version, ANY_VALUE(package_name) as sdk_package_name, ANY_VALUE(rh_app_name) as app_name, ANY_VALUE(sdk_ver) as sdk_ver, ANY_VALUE(app_type) as os_type, ANY_VALUE(os_ver) as os_version')
                ->whereIn('dev_str', $serverDevStrList)
                ->where('sdk_project_id', 116)
                ->where('date', '>=', now()->subDay()->toDateTimeString())
                ->groupBy('dev_str')
                ->getFromSR();
            // 处理数据
            $devStrInfoList = array_column($devStrInfoList, null, 'dev_str');
        }
        // 遍历list列表
        foreach ($list as $item) {
            // 补充设备信息
            if (empty($devStrInfoList[$item['server_dev_str']])) {
                continue;
            }
            $item = array_merge($item, $devStrInfoList[$item['server_dev_str']]);
            // 处理数据
            (new ExternalDataProcessService($item))->processData($item);
        }
        Log::info("执行同步AI数据的定时脚本，结束时间：" . now()->toDateTimeString());
    }
}
