<?php

/**
 * 对应starRocks数仓中anti_chect_upload_data_primary_v2表
 * @desc 对应starRocks数仓中anti_chect_upload_data_primary_v2表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/12/17
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Models\StarRocks;

class AntiChectUploadDataPrimaryV2 extends BaseModel
{
    public const TABLE_NAME = 'anti_chect_upload_data_primary_v2';

    protected $table = self::TABLE_NAME;
}
