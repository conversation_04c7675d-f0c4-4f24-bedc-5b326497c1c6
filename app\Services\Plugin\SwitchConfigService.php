<?php

/**
 * 开关配置服务类
 * @desc 开关配置服务类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/28
 */

namespace App\Services\Plugin;

use App\Models\MySQL\SwitchConfig;
use App\Services\MonitorConfigChangeService;
use App\Utils\RedisKey;
use Illuminate\Support\Facades\Redis;

class SwitchConfigService extends BaseService
{
    /**
     * 获取开关配置
     * @return array
     */
    public function getSwtichConfigList()
    {
        $configList = SwitchConfig::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->get()
            ->toArray();

        return $configList;
    }

    /**
     * 编辑开关
     * @return int
     */
    public function editSwtichConfig()
    {
        $config = SwitchConfig::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('os_type', $this->params['os_type'])
            ->first();
        if (!empty($config)) {
            // 更新操作
            return $this->modifyConfig($config["id"]);
        } else {
            // 添加操作
            return $this->insertConfig();
        }
    }

    /**
     * 添加开关配置
     * @return int
     */
    public function insertConfig()
    {
        $insertParams = array_merge($this->params, [
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        // 把risk_level字段转成json
        $insertParams['risk_level'] = json_encode($insertParams['risk_level']);

        $id = SwitchConfig::query()->insertGetId($insertParams);
        if (!empty($id)) {
            // 设置缓存
            $this->setRedis();
        }

        // 监控更改
        (new MonitorConfigChangeService(null, "外挂添加开关配置"))->monitor($insertParams);

        return $id;
    }

    /**
     * 编辑开关配置
     * @param $id
     * @return int
     */
    public function modifyConfig($id)
    {
        $updateParams = array_merge($this->params, [
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        // 把risk_level字段转成json
        $updateParams['risk_level'] = json_encode($updateParams['risk_level']);
        // 查询数据
        $config = SwitchConfig::query()->where('id', $id)->first();
        // 判断是否找到
        if (empty($config)) {
            return null;
        }
        // 创建监控服务类
        $service = new MonitorConfigChangeService($config->toArray(), "外挂修改开关配置");
        // 更新数据
        $ret = $config->update($updateParams);
        if (!empty($ret)) {
            // 设置缓存
            $this->setRedis();
        }
        // 监控
        $service->monitor($config->toArray());
        // 返回
        return $ret;
    }

    public function setRedis()
    {
        $list = $this->getSwtichConfigList();
        if (!empty($list)) {
            // 更新redis缓存
            Redis::connection('api')->set(RedisKey::getPluginSwitchInfoKey($this->params['developer_app_id']), json_encode($list));
        }
    }
}
