<?php

/**
 * 同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_v2表脚本
 * @desc 同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_v2表脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/12/17
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;
use App\Services\StarRocksService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncPrimaryDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:primary:data {time? : 时间}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_v2表脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        $time = $this->argument('time');
        if (empty($time)) {
            // 查询anti_chect_upload_data_primary_v2的最大时间
            $maxTime = AntiChectUploadDataPrimaryV2::query()
                ->selectRaw('max(stream_date) as max_time')
                ->firstFromSR();
            if (empty($maxTime['max_time'])) {
                $time = Carbon::now()->subHour()->toDateTimeString();
            } else {
                $time = Carbon::parse($maxTime['max_time'])->subHour()->toDateTimeString();
            }
        }

        try {
            // sql语句
            $sql = <<<SQL
insert into anti_chect_upload_data_primary_v2
select
session_id,
stream_date,
network_type,
application_info,
port_info,
socket_info,
click_info,
write_info,
is_write,
server_dev_str,
is_accessibility,
game_info,
is_plugin,
report_interval,
risk_level,
get_json_string(game_info,'account_id') as account_id,
get_json_string(game_info,'role_id') as role_id,
get_json_string(game_info,'role_name') as role_name,
get_json_string(game_info,'server_id') as server_id,
get_json_string(game_info,'server_name') as server_name,
is_game_upload,
hitbug_explain_desc,
hitbug_exception_image
from anti_chect_upload_data_primary
where stream_date >= '{$time}'
and session_id not in (select session_id from anti_chect_upload_data_primary_v2 where stream_date >= '{$time}')
SQL;
            // 同步数据表
            (new StarRocksService())->execute($sql);
            //打印日志
            Log::info("执行同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_v2表脚本完成, dateTime: {$time}, lastTime: " . date('Y-m-d H:i:s'));
        } catch (\Exception $e) {
            Log::error("执行同步anti_chect_upload_data_primary数据到anti_chect_upload_data_primary_v2表脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
