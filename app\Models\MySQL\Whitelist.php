<?php
/**
 * 白名单管理模型类
 * @desc 白名单管理模型类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/26
 */

namespace App\Models\MySQL;

use Illuminate\Database\Eloquent\SoftDeletes;

class Whitelist extends BaseModel
{
    use SoftDeletes;

    /**
     * 白名单类型
     */
    const WHITELIST_TYPE_SERVICE_DEV_STR = 1; // 设备白名单
    const WHITELIST_TYPE_IP = 2; // IP白名单
    const WHITELIST_TYPE_ACCOUNT_ID = 3; // 账号白名单

    /**
     *
     */
    const WHITELIST_TYPE_FIELD_MAP = [
        self::WHITELIST_TYPE_SERVICE_DEV_STR => 'server_dev_str',
        self::WHITELIST_TYPE_IP => 'ip',
        self::WHITELIST_TYPE_ACCOUNT_ID => 'account_id'
    ];

    /**
     * 白名单状态
     */
    const WHITELIST_STATUS_INVALID = 0; // 无效
    const WHITELIST_STATUS_VALID = 1; // 有效

    protected $table = 'whitelist';
    protected $primaryKey = 'whitelist_id';
    protected $fillable = [
        'developer_app_id', // 研发效能APP项目id
        'server_dev_str', // 设备ID
        'ip', // IP
        'account_id', // 账号ID
        'status', // 白名单名单状态，0：无效，1：有效，2：永久有效
        'whitelist_type', // 白名单类型，1：设备，2：IP，3：账号
        'start_date', // 白名单有效开始时间
        'end_date', // 白名单有效结束时间（白名单到期时间）
        'add_cause', // 添加原因
    ];

    // 设置日期字段的格式
    public $timestamps = false;
}
