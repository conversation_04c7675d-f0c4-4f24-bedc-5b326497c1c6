<?php

/**
 * 概览页控制器类
 * @desc 概览页控制器类
 * <AUTHOR> <EMAIL>
 * @date 2023/12/27
 */

namespace App\Http\Controllers;

use App\Http\Validation\OverviewValidation;
use App\Models\MySQL\SwitchConfig;
use App\Models\StarRocks\AntiChectInit;
use App\Services\Plugin\OverviewService;
use Exception;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class OverviewController extends Controller
{
    /**
     * 概览页-数据总览
     * @return array
     */
    public function summary()
    {
        // 占比就是开启外挂设备数/初始化设备数
        // 环比就是比如你选择的是今天的数据   那么环比就是对比昨天的
        try {
            $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
            $diff = strtotime($params["end_date"]) - strtotime($params["start_date"]);
            $currentService = new OverviewService($params);

            // 联网设备数
            $connectedDevNum = $currentService->getConnectedDevNum();
            // 启动外挂设备数
            $useChectDevNum = $currentService->getUseChectDevNum();
            // 启动用户数
            $useUserNum = $currentService->getUseUserNum();
            // 启动外挂用户数
            $useChectUserNum = $currentService->getUseChectUserNum();

            $params["start_date"] = date("Y-m-d H:i:s", (strtotime($params["start_date"]) - $diff));
            $params["end_date"] = date("Y-m-d H:i:s", (strtotime($params["end_date"]) - $diff));
            $preService = new OverviewService($params);

            // 环比联网设备数
            $preConnectedDevNum = $preService->getConnectedDevNum();
            // 环比启动外挂设备数
            $preUseChectDevNum = $preService->getUseChectDevNum();
            // 环比启动用户数
            $preUseUserNum = $preService->getUseUserNum();
            // 环比启动外挂用户数
            $preUseChectUserNum = $preService->getUseChectUserNum();

            // 联网设备数环比
            $connectedDevSeqPercent = $currentService->percentFormat($connectedDevNum, $preConnectedDevNum, 2);
            // 启动外挂设备数占比（开启外挂设备数/初始化设备数）
            $useChectDevNumPercent = $currentService->percentFormat($useChectDevNum, $connectedDevNum);
            // 启动外挂设备数环比
            $useChectDevNumSeqPercent = $currentService->percentFormat($useChectDevNum, $preUseChectDevNum, 2);
            // 启动用户数环比
            $useUserNumSeqPercent = $currentService->percentFormat($useUserNum, $preUseUserNum, 2);
            // 启动外挂用户数环比
            $useChectUserNumSeqPercent = $currentService->percentFormat($useChectUserNum, $preUseChectUserNum, 2);
            // 启动外挂用户数占比（开启外挂用户数/启动用户数）
            $useChectUserNumPercent = $currentService->percentFormat($useChectUserNum, $useUserNum);

            // 判断是否接入了外挂检测
            $count = AntiChectInit::query()->selectRaw('count(*) as num')->where('extra_app_id', $params['extra_app_id'])->firstFromSR();

            $ret = [
                "connected_dev_num" => $connectedDevNum,
                "connected_dev_seq_percent" => $connectedDevSeqPercent,
                "use_chect_dev_num" => $useChectDevNum,
                "use_chect_dev_num_percent" => $useChectDevNumPercent,
                "use_chect_dev_num_seq_percent" => $useChectDevNumSeqPercent,
                "use_user_num" => $useUserNum,
                "use_user_num_seq_percent" => $useUserNumSeqPercent,
                "use_chect_user_num" => $useChectUserNum,
                "use_chect_user_num_percent" => $useChectUserNumPercent,
                "use_chect_user_num_seq_percent" => $useChectUserNumSeqPercent,
                "is_use" => ($count['num'] ?? 0) > 0,
            ];

            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-数据总览接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-统计图
     * @return array
     */
    public function trend()
    {
        try {
            //返回数据
            $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
            $service = new OverviewService($params);
            $connectedDevNumList = $service->getConnectedDevNumTrend();
            if (!empty($connectedDevNumList)) {
                $connectedDevNumList = array_column($connectedDevNumList, "connected_dev_num", "date");
            }
            $useChectDevNumList = $service->getUseChectDevNumTrend();
            if (!empty($useChectDevNumList)) {
                $useChectDevNumList = array_column($useChectDevNumList, "use_chect_dev_num", "date");
            }
            $useUserNumList = $service->getUseUserNumTrend();
            if (!empty($useUserNumList)) {
                $useUserNumList = array_column($useUserNumList, "use_user_num", "date");
            }
            $useChectUserNumList = $service->getUseChectUserNumTrend();
            if (!empty($useChectUserNumList)) {
                $useChectUserNumList = array_column($useChectUserNumList, "use_chect_user_num", "date");
            }

            // 用0填充没有数据的日期数据
            $startTime = strtotime($params["start_date"]);
            $endTime = strtotime($params["end_date"]);
            $mod = ($endTime - $startTime) / (3600 * 24);
            $ret = [];
            while (true) {
                $step = 3600 * 24;
                $dateFormat = "Y-m-d";
                if ($mod <= 2) {
                    $step = 3600;
                    $dateFormat = "Y-m-d H:00:00";
                }

                // 初始化数据
                $listDate = date($dateFormat, $startTime);
                $ret[$listDate] = [
                    "date" => $listDate,
                    "connected_dev_num" => 0,
                    "use_chect_dev_num" => 0,
                    "use_user_num" => 0,
                    "use_chect_user_num" => 0,
                ];
                if (isset($connectedDevNumList[$listDate])) {
                    $ret[$listDate]["connected_dev_num"] = $connectedDevNumList[$listDate];
                }
                if (isset($useChectDevNumList[$listDate])) {
                    $ret[$listDate]["use_chect_dev_num"] = $useChectDevNumList[$listDate];
                }
                if (isset($useUserNumList[$listDate])) {
                    $ret[$listDate]["use_user_num"] = $useUserNumList[$listDate];
                }
                if (isset($useChectUserNumList[$listDate])) {
                    $ret[$listDate]["use_chect_user_num"] = $useChectUserNumList[$listDate];
                }

                $startTime = $startTime + $step;
                if ($startTime > $endTime) {
                    break;
                }
            }

            return $this->success(array_values($ret));
        } catch (Exception $e) {
            Log::error('外挂检测概览页-统计图接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-外挂设备品牌排行榜
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function chectDevBrandRank()
    {
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
        $service = new OverviewService($params);
        $ret = $service->getChectDevBrandRank();
        try {
            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-外挂设备品牌排行榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-外挂设备IP排行榜
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function chectDevIPRank()
    {
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
        $service = new OverviewService($params);
        $ret = $service->getChectDevIPRank();
        try {
            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-外挂设备IP排行榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-外挂应用列表排行
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function chectAppRank()
    {
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
        $service = new OverviewService($params);
        $ret = $service->getChectAppRank();
        try {
            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-外挂应用列表排行接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-外挂设备系统版本排行榜
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function chectDevSystemRank()
    {
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
        $service = new OverviewService($params);
        $list = $service->getChectDevSystemRank();
        $ret = [];
        foreach ($list as $key => $val) {
            $osText = SwitchConfig::OS_TYPE_MAP[$val["os_type"]] ?? "unknown";
            $osText = $osText . "_" . $val["os_version"];
            $ret[] = [
                "system_version" => $osText,
                "dev_num" => $val["dev_num"],
            ];
        }
        try {
            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-外挂设备系统版本排行榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 概览页-外挂设备执行动作排行榜
     *
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function chectDevActionRank()
    {
        $params = OverviewValidation::build()->extraAppId()->startDate()->endDate()->osType()->validate();
        $service = new OverviewService($params);
        $ret = $service->getChectDevActionRank();
        try {
            //返回数据
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('外挂检测概览页-外挂设备执行动作排行榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
