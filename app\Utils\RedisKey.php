<?php

/**
 * Redis的Key枚举
 * @desc Redis的Key枚举
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Utils;

class RedisKey
{
    /**
     * 外挂报告结果的key
     *
     * @param $sessionId
     * @return string
     */
    public static function getPluginSessionResultKey($sessionId)
    {
        return sprintf('plugin:session:result:%s', $sessionId);
    }

    /**
     * 反外挂开关信息key
     * @param $extraAppId
     * @return string
     */
    public static function getPluginSwitchInfoKey($extraAppId)
    {
        return sprintf('chect:switch_config:%s', $extraAppId);
    }

    /**
     * 反外挂log最新时间的key
     * 
     * @return string
     */
    public static function getPluginLogDateKey()
    {
        return sprintf('plugin:log:date');
    }

    /**
     * 外挂列表数据导出的key
     * 
     * @return string
     */
    public static function getPluginExportDataKey($taskId)
    {
        return sprintf('plugin:export:data:%s', $taskId);
    }
}
