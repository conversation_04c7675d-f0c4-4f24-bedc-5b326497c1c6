<?php

namespace App\Services;

use App\Services\aw\Db\Reader;
use App\Services\aw\Db\Reader\InvalidDatabaseException;

class IpServer
{
    private static $instance;
    /**
     * @var Reader
     */
    private $reader;

    /**
     */
    private function __construct()
    {
        $this->reader = new Reader(resource_path('assets' . DIRECTORY_SEPARATOR . 'IP_city_single_WGS84.awdb'));
    }

    /** 单例访问统一入口
     * @return IpServer
     */
    public static function getInstance(): IpServer
    {
        if (!(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 获取所有的返回数据
     * @param string $ip
     * @return mixed
     */
    public function getResultAll(string $ip)
    {
        $reader = $this->reader;
        try {
            $result = json_encode($reader->get($ip), JSON_UNESCAPED_UNICODE);
            return json_decode($result, true);
        } catch (InvalidDatabaseException $e) {
            return null;
        }
    }

    /**
     * 获取ip对应province
     * @param string $ip
     * @return string
     */
    public function getProvince(string $ip): string
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP)) return '';
        $resArr = $this->getResultAll($ip);
        return $resArr['province'] ?? '';
    }

    /** 获取ip
     * @param $ip
     * @return mixed|string
     */
    public function getCity($ip)
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP)) return '';
        try {
            $resArr = $this->getResultAll($ip);
            return $resArr['city'] ?? '';
        } catch (\Exception $e) {
            return '';
        }
    }


    /**获取IP归属地
     * 国内返回城市，国外返回国家
     * @param string $ip
     * @return array|mixed|string|string[]|null
     */
    public function getIpHome(string $ip)
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP)) return '';
        try {
            $countryCode = $this->getResultAll($ip);
            if (isset($countryCode['areacode']) && $countryCode['areacode'] == 'CN') {
                $province = $this->getProvince($ip);
                return preg_replace('/省|市|自治区|特别行政区|壮族|回族|维吾尔/', '', $province);
            } else {
                return $countryCode['country'] ?? '';
            }
        } catch (\Exception $e) {
            return '';
        }
    }
}
