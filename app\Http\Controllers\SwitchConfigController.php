<?php

/**
 * 开关控制控制器
 * @desc 开关控制控制器
 * <AUTHOR> <EMAIL>
 * @date 2023/12/28
 */

namespace App\Http\Controllers;

use App\Http\Validation\SwitchConfigValidation;
use App\Models\MySQL\SwitchConfig;
use App\Services\Plugin\SwitchConfigService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class SwitchConfigController extends Controller
{
    /**
     * 开关配置信息
     * @doc
     * @throws ValidationException
     */
    public function getConfigInfo()
    {
        $params = SwitchConfigValidation::build()->developerAppId()->validate();
        try {
            $ret = [];
            $configList = (new SwitchConfigService($params))->getSwtichConfigList();
            if (!empty($configList)) {
                foreach ($configList as $val) {
                    $osMap = SwitchConfig::OS_TYPE_MAP;
                    $osText = $osMap[$val['os_type']];
                    // 把risk_level字段转成数组
                    $val['risk_level'] = json_decode($val['risk_level'], true);
                    // 判断是否成功转成数组，如果没有转成数组，就把值放到数组里
                    if (!is_array($val['risk_level'])) {
                        $val['risk_level'] = [$val['risk_level']];
                    }
                    $ret[$osText] = $val;
                }
            }
            // 没有值的，默认填充上默认值
            if (!isset($ret["android"])) {
                $ret["android"] = SwitchConfig::FILL_ANDROID_DEFAULT_CONFIG;
            }
            if (!isset($ret["ios"])) {
                $ret["ios"] = SwitchConfig::FILL_IOS_DEFAULT_CONFIG;
            }
            if (!isset($ret["pc"])) {
                $ret["pc"] = SwitchConfig::FILL_PC_DEFAULT_CONFIG;
            }
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('配置页-获取开关配置信息接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 开关配置编辑
     * @doc
     * @throws ValidationException
     */
    public function edit()
    {
        $params = SwitchConfigValidation::build()->developerAppId()->osType()
            ->isActive()->errorReportInterval()
            ->riskLevel()->execAction()->popupContent()
            ->crashStartFrequency()->crashEndFrequency()
            ->validate();
        try {
            $ret = (new SwitchConfigService($params))->editSwtichConfig();
            if (empty($ret)) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, "编辑失败");
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('配置页-开关配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
